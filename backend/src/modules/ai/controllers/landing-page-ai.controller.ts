import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AiService } from '../services/ai.service';

interface GenerateHeadlineDto {
  business: string;
  target: string;
  goals: string;
  audience?: string;
  painPoints?: string;
}

interface GenerateFeaturesDto {
  business: string;
  benefits: string;
  audience: string;
}

interface GenerateContentDto {
  business: string;
  target: string;
  goals: string;
  audience: string;
  painPoints: string;
  benefits: string;
}

@ApiTags('Landing Page AI')
@Controller('ai/landing-page')
export class LandingPageAiController {
  constructor(private readonly aiService: AiService) {}

  @Post('generate-headline')
  @ApiOperation({ summary: '生成Landing Page主标题和副标题' })
  @ApiResponse({ status: 200, description: '成功生成标题' })
  async generateHeadline(@Body() dto: GenerateHeadlineDto) {
    try {
      const prompt = `
请为以下业务生成一个吸引人的Landing Page主标题和副标题：

业务描述: ${dto.business}
目标客户: ${dto.target}
业务目标: ${dto.goals}
${dto.audience ? `详细受众: ${dto.audience}` : ''}
${dto.painPoints ? `客户痛点: ${dto.painPoints}` : ''}

要求：
1. 主标题要简洁有力，突出核心价值主张
2. 副标题要详细说明产品能解决什么问题
3. 符合中文表达习惯
4. 具有说服力和吸引力

请以JSON格式回复，包含headline和subheadline字段。
`;

      const response = await this.aiService.chat({
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 200,
        character: '营销文案专家'
      });

      if (!response.success) {
        throw new Error(response.error || '生成失败');
      }

      const aiResponse = response.data;

      if (!aiResponse) {
        throw new Error('AI响应为空');
      }

      // 尝试解析JSON，如果失败则返回结构化数据
      try {
        const parsed = JSON.parse(aiResponse);
        return parsed;
      } catch {
        // 如果AI返回的不是标准JSON，则手动构建
        const lines = aiResponse.split('\n').filter(line => line.trim());
        return {
          headline: lines[0] || `${dto.business}专业解决方案 - 助您实现${dto.goals}`,
          subheadline: lines[1] || `为${dto.target}量身定制，快速高效的专业服务`
        };
      }
    } catch (error) {
      throw new HttpException(
        '生成标题失败，请重试',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('generate-features')
  @ApiOperation({ summary: '生成产品功能特色' })
  @ApiResponse({ status: 200, description: '成功生成功能特色' })
  async generateFeatures(@Body() dto: GenerateFeaturesDto) {
    try {
      const prompt = `
请为以下业务生成4个核心功能特色：

业务描述: ${dto.business}
产品优势: ${dto.benefits}
目标受众: ${dto.audience}

要求：
1. 每个特色简洁明了，3-8个字
2. 突出实用性和吸引力
3. 符合目标受众需求
4. 避免空泛的描述

请以数组格式回复，如：["专业可靠", "快速响应", "性价比高", "贴心服务"]
`;

      const response = await this.aiService.chat({
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        max_tokens: 150,
        character: '营销文案专家'
      });

      if (!response.success) {
        throw new Error(response.error || '生成失败');
      }

      const aiResponse = response.data;

      if (!aiResponse) {
        throw new Error('AI响应为空');
      }

      // 尝试解析JSON数组
      try {
        const parsed = JSON.parse(aiResponse);
        if (Array.isArray(parsed)) {
          return { features: parsed.slice(0, 4) };
        }
      } catch {
        // 如果解析失败，返回默认特色
        return {
          features: [
            '专业可靠的服务',
            '快速响应支持', 
            '性价比超高',
            '客户满意保证'
          ]
        };
      }

      return {
        features: [
          '专业可靠的服务',
          '快速响应支持',
          '性价比超高', 
          '客户满意保证'
        ]
      };
    } catch (error) {
      throw new HttpException(
        '生成功能特色失败，请重试',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('generate-cta')
  @ApiOperation({ summary: '生成行动召唤按钮文字' })
  @ApiResponse({ status: 200, description: '成功生成CTA文字' })
  async generateCta(@Body() dto: { business: string; goals: string }) {
    try {
      const prompt = `
请为以下业务生成一个有吸引力的行动召唤按钮文字：

业务描述: ${dto.business}
业务目标: ${dto.goals}

要求：
1. 3-8个字
2. 使用动作词汇
3. 创造紧迫感
4. 符合中文习惯

请只回复按钮文字，不要其他内容。
`;

      const response = await this.aiService.chat({
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.5,
        max_tokens: 50,
        character: '营销文案专家'
      });

      if (!response.success) {
        throw new Error(response.error || '生成失败');
      }

      const aiResponse = response.data;

      if (!aiResponse) {
        throw new Error('AI响应为空');
      }

      return { cta: aiResponse.trim() || '立即开始' };
    } catch (error) {
      throw new HttpException(
        '生成CTA文字失败，请重试',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('optimize-content')
  @ApiOperation({ summary: '优化Landing Page整体内容' })
  @ApiResponse({ status: 200, description: '成功优化内容' })
  async optimizeContent(@Body() dto: GenerateContentDto) {
    try {
      const prompt = `
请为以下业务优化Landing Page的整体内容：

业务描述: ${dto.business}
目标客户: ${dto.target}
业务目标: ${dto.goals}
目标受众: ${dto.audience}
客户痛点: ${dto.painPoints}
产品优势: ${dto.benefits}

请生成：
1. 优化的主标题
2. 优化的副标题
3. 4个核心功能特色
4. 行动召唤按钮文字
5. 一段价值主张描述

请以JSON格式回复，包含headline, subheadline, features(数组), cta, valueProposition字段。
`;

      const response = await this.aiService.chat({
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 400,
        character: '营销文案专家'
      });

      if (!response.success) {
        throw new Error(response.error || '生成失败');
      }

      const aiResponse = response.data;

      if (!aiResponse) {
        throw new Error('AI响应为空');
      }

      try {
        const parsed = JSON.parse(aiResponse);
        return parsed;
      } catch {
        // 如果解析失败，返回基础优化内容
        return {
          headline: `${dto.business}专业解决方案`,
          subheadline: `为${dto.audience}解决${dto.painPoints}，实现${dto.goals}`,
          features: ['专业服务', '快速响应', '高性价比', '满意保证'],
          cta: '立即咨询',
          valueProposition: `我们专注于为${dto.target}提供${dto.business}服务，通过${dto.benefits}帮助您实现业务目标。`
        };
      }
    } catch (error) {
      throw new HttpException(
        '优化内容失败，请重试',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
} 