import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@/modules/user/entities/user.entity';
import { AdminController } from './controllers/admin.controller';
import { AdminService } from './services/admin.service';
import { StatsService } from './services/stats.service';
import { AiModule } from '@/modules/ai/ai.module';
import { AiProviderController } from './controllers/ai-provider.controller';
import { AdminAiProviderService } from './services/ai-provider.service';
import { AiProvider } from '@/modules/ai/entities/ai-provider.entity';
import { AdminUsersController } from './controllers/admin-users.controller';
import { AdminUsersService } from './services/admin-users.service';
import { AdminCategoriesController } from './controllers/categories.controller';
import { AdminBuildingToolsController } from './controllers/building-tools.controller';
import { Catalogue } from '@/modules/course/entities/catalogue.entity';
import { BuildingTool } from '@/modules/course/entities/building-tool.entity';
import { Course } from '@/modules/course/entities/course.entity';
import { CatalogueService } from '@/modules/course/services/catalogue.service';
import { BuildingToolService } from '@/modules/course/services/building-tool.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, AiProvider, Catalogue, BuildingTool, Course]),
    AiModule,
  ],
  controllers: [
    AdminController,
    AiProviderController,
    AdminUsersController,
    AdminCategoriesController,
    AdminBuildingToolsController,
  ],
  providers: [
    AdminService,
    StatsService,
    AdminAiProviderService,
    AdminUsersService,
    CatalogueService,
    BuildingToolService,
  ],
})
export class AdminModule {} 