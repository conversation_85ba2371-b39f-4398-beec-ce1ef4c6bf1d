import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { Course } from '../entities/course.entity';
import { CourseLesson } from '../entities/course-lesson.entity';
import { UserCourse } from '../entities/user-course.entity';
import * as fs from 'fs/promises';
import * as path from 'path';

@Injectable()
export class CourseService {
  private readonly logger = new Logger(CourseService.name);

  constructor(
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(CourseLesson)
    private readonly lessonRepository: Repository<CourseLesson>,
    @InjectRepository(UserCourse)
    private readonly userCourseRepository: Repository<UserCourse>,
  ) {}

  /**
   * 获取所有课程
   */
  async findAll(includeUnpublished = false) {
    const query = this.courseRepository.createQueryBuilder('course');

    if (!includeUnpublished) {
      query.where('course.isPublished = :isPublished', { isPublished: true });
    }

    return query.getMany();
  }

  /**
   * 获取课程详情
   */
  async findOne(id: string) {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: ['lessons'],
    });

    if (!course) {
      throw new NotFoundException(`Course #${id} not found`);
    }

    return course;
  }

  /**
   * 获取课程章节
   */
  async findLessons(courseId: string) {
    return this.lessonRepository.find({
      where: { courseId },
      order: { order: 'ASC' },
    });
  }

  /**
   * 检查用户是否已购买课程
   */
  async hasUserPurchased(userId: string, courseId: string): Promise<boolean> {
    const count = await this.userCourseRepository.count({
      where: { userId, courseId },
    });
    return count > 0;
  }

  /**
   * 获取课程价格
   */
  async getCoursePrice(courseId: string): Promise<number> {
    const course = await this.courseRepository.findOne({
      where: { id: courseId },
      select: ['price'],
    });

    if (!course) {
      throw new NotFoundException(`Course #${courseId} not found`);
    }

    return course.price;
  }

  /**
   * 获取用户课程记录
   */
  async getUserCourse(userId: string, courseId: string): Promise<UserCourse> {
    const userCourse = await this.userCourseRepository.findOne({
      where: { userId, courseId },
    });

    if (!userCourse) {
      throw new NotFoundException('User course record not found');
    }

    return userCourse;
  }

  /**
   * 获取用户课程进度
   */
  async getUserProgress(userId: string, courseId: string): Promise<number> {
    try {
      const userCourse = await this.getUserCourse(userId, courseId);
      return userCourse.progress;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 授予用户课程访问权限
   */
  async grantCourseAccess(
    userId: string,
    courseId: string,
    entityManager?: EntityManager,
  ): Promise<void> {
    this.logger.debug('Granting course access', { userId, courseId });
    const manager = entityManager || this.userCourseRepository.manager;

    // 检查课程是否存在
    this.logger.debug('Checking if course exists', { courseId });
    const course = await manager.findOne(Course, {
      where: { id: courseId },
    });

    if (!course) {
      this.logger.error('Course not found', { courseId });
      throw new NotFoundException(`Course #${courseId} not found`);
    }
    this.logger.debug('Course found', { courseId, title: course.title });

    // 检查是否已经购买
    this.logger.debug('Checking if user already has access', { userId, courseId });
    const existing = await manager.findOne(UserCourse, {
      where: { userId, courseId },
    });

    if (existing) {
      this.logger.warn('User already has access to this course', {
        userId,
        courseId,
      });
      return;
    }
    this.logger.debug('User does not have access yet', { userId, courseId });

    try {
      // 创建用户课程记录
      this.logger.debug('Creating user course record', { userId, courseId });
      const userCourse = manager.create(UserCourse, {
        userId,
        courseId,
        progress: 0,
        completedLessons: [],
      });

      await manager.save(UserCourse, userCourse);
      this.logger.log(`Successfully granted course access - userId: ${userId}, courseId: ${courseId}`);
    } catch (error) {
      this.logger.error('Failed to grant course access', {
        error: error instanceof Error ? error.message : error,
        userId,
        courseId,
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * 查找课程章节
   */
  async findLesson(courseId: string, lessonId: string): Promise<CourseLesson | null> {
    // 尝试将 lessonId 转换为数字，如果成功则按 order 查找，否则按 id 查找
    const order = parseInt(lessonId);
    if (!isNaN(order)) {
      return this.lessonRepository.findOne({
        where: { courseId, order },
      });
    }

    return this.lessonRepository.findOne({
      where: { courseId, id: lessonId },
    });
  }

  /**
   * 获取课程章节内容
   */
  async getLessonContent(courseId: string, lessonId: string): Promise<string> {
    this.logger.log('Getting lesson content:', {
      courseId,
      lessonId,
      timestamp: new Date().toISOString(),
    });

    // 直接查询章节，确保它存在且属于正确的课程
    const lesson = await this.lessonRepository.findOne({
      where: {
        id: lessonId,
        courseId: courseId
      }
    });

    if (!lesson) {
      this.logger.error('Lesson not found or does not belong to the course:', {
        courseId,
        lessonId,
        timestamp: new Date().toISOString(),
      });
      throw new NotFoundException('课程章节不存在');
    }

    // 从文件系统读取课程内容
    try {
      // 构建完整的文件路径
      const filePath = path.join(process.cwd(), 'article', lesson.scriptPath);
      this.logger.debug(`Reading lesson content from: ${filePath}`);

      const content = await fs.readFile(filePath, 'utf-8');

      this.logger.log('Successfully read lesson content:', {
        courseId,
        lessonId,
        filePath,
        contentLength: content.length,
        timestamp: new Date().toISOString(),
      });

      return content;
    } catch (error) {
      this.logger.error('Failed to read lesson content:', {
        courseId,
        lessonId,
        scriptPath: lesson.scriptPath,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
      throw new Error('无法读取课程内容：' + error.message);
    }
  }

  /**
   * 更新学习进度
   */
  async updateProgress(userId: string, courseId: string, lessonId: string): Promise<void> {
    const userCourse = await this.userCourseRepository.findOne({
      where: { userId, courseId },
    });

    if (!userCourse) {
      throw new NotFoundException('User course record not found');
    }

    // 更新最后访问的章节
    userCourse.lastVisitedLessonId = lessonId;

    // 如果这个章节还没有完成，添加到已完成列表
    if (!userCourse.completedLessons.includes(lessonId)) {
      userCourse.completedLessons.push(lessonId);
    }

    // 计算总进度
    const totalLessons = await this.lessonRepository.count({
      where: { courseId },
    });

    // 确保进度不超过100%
    const progress = Math.min(
      Math.round((userCourse.completedLessons.length / totalLessons) * 100),
      100
    );

    userCourse.progress = progress;
    await this.userCourseRepository.save(userCourse);
  }

  /**
   * 获取用户已购买的课程
   */
  async getPurchasedCourses(userId: string) {
    const userCourses = await this.userCourseRepository.find({
      where: { userId },
      relations: ['course'],
    });

    return userCourses.map(uc => ({
      ...uc.course,
      progress: uc.progress,
      isPurchased: true,
    }));
  }

  /**
   * 创建课程
   */
  async create(courseData: Partial<Course>): Promise<Course> {
    const course = this.courseRepository.create({
      ...courseData,
      isPublished: false,
      lessonsCount: 0,
    });
    return this.courseRepository.save(course);
  }

  /**
   * 更新课程
   */
  async update(id: string, courseData: Partial<Course>): Promise<Course> {
    const course = await this.findOne(id);

    // 更新课程信息
    Object.assign(course, courseData);

    return this.courseRepository.save(course);
  }

  /**
   * 删除课程
   */
  async delete(id: string): Promise<void> {
    const course = await this.findOne(id);

    // 删除相关的用户课程记录
    await this.userCourseRepository.delete({ courseId: id });

    // 删除课程章节
    await this.lessonRepository.delete({ courseId: id });

    // 删除课程
    await this.courseRepository.remove(course);
  }

  /**
   * 发布课程
   */
  async publish(id: string): Promise<Course> {
    const course = await this.findOne(id);
    course.isPublished = true;
    return this.courseRepository.save(course);
  }

  /**
   * 取消发布课程
   */
  async unpublish(id: string): Promise<Course> {
    const course = await this.findOne(id);
    course.isPublished = false;
    return this.courseRepository.save(course);
  }

  /**
   * 切换课程发布状态
   */
  async togglePublish(id: string): Promise<Course> {
    const course = await this.findOne(id);
    course.isPublished = !course.isPublished;
    return this.courseRepository.save(course);
  }

  /**
   * 创建课程章节
   */
  async createLesson(courseId: string, lessonData: Partial<CourseLesson>): Promise<CourseLesson> {
    // 检查课程是否存在
    const course = await this.courseRepository.findOne({
      where: { id: courseId },
      select: ['id', 'code']
    });

    if (!course) {
      throw new NotFoundException(`Course #${courseId} not found`);
    }

    // 获取当前课程的最大章节序号
    const result = await this.lessonRepository
      .createQueryBuilder('lesson')
      .select('MAX(lesson.order)', 'maxOrder')
      .where('lesson.courseId = :courseId', { courseId })
      .getRawOne();

    const nextOrder = (result.maxOrder || 0) + 1;

    // 自动生成脚本路径
    const scriptPath = `${course.code}/lesson_${nextOrder}_script.md`;

    try {
      // 开启事务
      return await this.lessonRepository.manager.transaction(async transactionalEntityManager => {
        // 直接插入章节
        const insertResult = await transactionalEntityManager
          .createQueryBuilder()
          .insert()
          .into(CourseLesson)
          .values({
            title: lessonData.title,
            description: lessonData.description,
            duration: lessonData.duration,
            courseId: courseId,
            order: nextOrder,
            scriptPath: scriptPath
          })
          .execute();

        // 更新课程章节数
        await transactionalEntityManager
          .createQueryBuilder()
          .update(Course)
          .set({ lessonsCount: () => 'lessons_count + 1' })
          .where('id = :id', { id: courseId })
          .execute();

        // 创建脚本文件
        const dirPath = path.join(process.cwd(), 'article', course.code);
        await fs.mkdir(dirPath, { recursive: true });

        const filePath = path.join(process.cwd(), 'article', scriptPath);
        await fs.writeFile(filePath, '', 'utf-8');

        // 返回创建的章节
        return insertResult.raw[0];
      });
    } catch (error) {
      this.logger.error(`Failed to create lesson: ${error.message}`, {
        courseId,
        lessonData,
        error: error.stack,
      });
      throw error;
    }
  }

  /**
   * 更新课程章节
   */
  async updateLesson(
    courseId: string,
    lessonId: string,
    lessonData: Partial<CourseLesson>
  ): Promise<CourseLesson> {
    const lesson = await this.lessonRepository.findOne({
      where: { id: lessonId, courseId },
    });

    if (!lesson) {
      throw new NotFoundException('课程章节不存在');
    }

    // 更新章节信息
    Object.assign(lesson, lessonData);
    return this.lessonRepository.save(lesson);
  }

  /**
   * 删除课程章节
   */
  async deleteLesson(courseId: string, lessonId: string): Promise<void> {
    const lesson = await this.lessonRepository.findOne({
      where: { id: lessonId, courseId },
    });

    if (!lesson) {
      throw new NotFoundException('课程章节不存在');
    }

    // 删除章节
    await this.lessonRepository.remove(lesson);

    // 更新课程的章节数
    const course = await this.findOne(courseId);
    course.lessonsCount -= 1;
    await this.courseRepository.save(course);
  }

  /**
   * 更新课程章节内容
   */
  async updateLessonContent(courseId: string, lessonId: string, content: string): Promise<void> {
    this.logger.log('Updating lesson content:', {
      courseId,
      lessonId,
      contentLength: content.length,
      timestamp: new Date().toISOString(),
    });

    // 直接查询章节，确保它存在且属于正确的课程
    const lesson = await this.lessonRepository.findOne({
      where: {
        id: lessonId,
        courseId: courseId
      }
    });

    if (!lesson) {
      this.logger.error('Lesson not found or does not belong to the course:', {
        courseId,
        lessonId,
        timestamp: new Date().toISOString(),
      });
      throw new NotFoundException('课程章节不存在');
    }

    try {
      // 构建文件路径
      const filePath = path.join(process.cwd(), 'article', lesson.scriptPath);
      this.logger.debug(`Writing lesson content to: ${filePath}`);

      // 确保目录存在
      await fs.mkdir(path.dirname(filePath), { recursive: true });

      // 写入内容
      await fs.writeFile(filePath, content, 'utf-8');

      this.logger.log('Successfully updated lesson content:', {
        courseId,
        lessonId,
        filePath,
        contentLength: content.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Failed to update lesson content:', {
        courseId,
        lessonId,
        scriptPath: lesson.scriptPath,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });
      throw new Error('更新课程内容失败：' + error.message);
    }
  }

  /**
   * 重新排序课程章节
   */
  async reorderLessons(courseId: string, lessonIds: string[]): Promise<void> {
    await this.lessonRepository.manager.transaction(async transactionalEntityManager => {
      // 验证所有章节是否属于该课程
      const lessons = await transactionalEntityManager.find(CourseLesson, {
        where: { courseId },
      });

      const lessonMap = new Map(lessons.map(lesson => [lesson.id, lesson]));

      // 验证传入的章节ID是否都存在且属于该课程
      if (lessonIds.length !== lessons.length ||
          !lessonIds.every(id => lessonMap.has(id))) {
        throw new Error('无效的章节顺序');
      }

      // 第一步：将所有章节的顺序设置为临时值（使用负数）
      for (let i = 0; i < lessonIds.length; i++) {
        const lessonId = lessonIds[i];
        await transactionalEntityManager.update(
          CourseLesson,
          { id: lessonId },
          { order: -(i + 1000) } // 使用足够小的负数作为临时值
        );
      }

      // 第二步：设置最终的顺序值
      for (let i = 0; i < lessonIds.length; i++) {
        const lessonId = lessonIds[i];
        await transactionalEntityManager.update(
          CourseLesson,
          { id: lessonId },
          { order: i + 1 }
        );
      }
    });
  }

  /**
   * 购买课程
   */
  async purchaseCourse(courseId: string, userId: string): Promise<void> {
    // 检查课程是否存在
    const course = await this.findOne(courseId);
    if (!course) {
      throw new NotFoundException(`Course #${courseId} not found`);
    }

    // 检查是否已经购买
    const hasPurchased = await this.hasUserPurchased(userId, courseId);
    if (hasPurchased) {
      return; // 已经购买过，直接返回
    }

    // 授予课程访问权限
    await this.grantCourseAccess(userId, courseId);
  }
}