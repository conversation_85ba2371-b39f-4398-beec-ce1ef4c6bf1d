import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Course } from './entities/course.entity';
import { CourseLesson } from './entities/course-lesson.entity';
import { UserCourse } from './entities/user-course.entity';
import { BuildingTool } from './entities/building-tool.entity';
import { CourseService } from './services/course.service';
import { BuildingToolService } from './services/building-tool.service';
import { CourseController } from './controllers/course.controller';
import { UploadController } from './controllers/upload.controller';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { CourseGeneratorService } from './services/course-generator.service';
import { CourseGeneratorController } from './controllers/course-generator.controller';
import { AiModule } from '../ai/ai.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Course, CourseLesson, UserCourse, BuildingTool]),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', '..', '..', 'uploads'),
      serveRoot: '/uploads',
    }),
    AiModule,
  ],
  controllers: [CourseController, UploadController, CourseGeneratorController],
  providers: [CourseService, CourseGeneratorService, BuildingToolService],
  exports: [CourseService, BuildingToolService],
})
export class CourseModule {}
