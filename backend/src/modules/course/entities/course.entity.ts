import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToMany } from 'typeorm';
import { CourseLesson } from './course-lesson.entity';
import { BuildingTool } from './building-tool.entity';

export enum CourseLevelType {
  BASIC = '综合基础',
  INTERMEDIATE = '专项进阶',
  ADVANCED = '技术高阶'
}

@Entity('courses')
export class Course {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  code: string;  // 例如：'course_1_enterprise_ai_basic'

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: CourseLevelType,
    enumName: 'course_level_type_enum'
  })
  level: CourseLevelType;

  @Column()
  duration: string;  // 例如：'8小时'

  @Column({ name: 'lessons_count', default: 0 })
  lessonsCount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ name: 'is_published', default: false })
  isPublished: boolean;

  @OneToMany(() => CourseLesson, lesson => lesson.course)
  lessons: CourseLesson[];

  @ManyToMany(() => BuildingTool, tool => tool.courses)
  buildingTools: BuildingTool[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 