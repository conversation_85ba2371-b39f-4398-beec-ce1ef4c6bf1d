import { Injectable, UnauthorizedException, BadRequestException, Logger, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { UserService } from '../../user/services/user.service';
import { LoginDto } from '../dto/login.dto';
import { RegisterDto } from '../dto/register.dto';
import { JwtPayload } from '../interfaces/jwt-payload.interface';
import { AUTH_ERRORS, JWT_CONFIG } from '../constants/auth.constants';
import { User } from '../../user/entities/user.entity';
import { UserRole, UserStatus } from '../../user/entities/user.entity';
import { Response } from 'express';
import { ENV_CONFIG } from '@/config/environment';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  private setCookies(response: Response, token: string) {
    const envType = ENV_CONFIG.isDevelopment ? 'development' : 'production';
    const cookieConfig = {
      ...ENV_CONFIG.cookie.common,
      ...ENV_CONFIG.cookie[envType],
    };

    console.log('[AuthService] 设置认证 Cookie', {
      environment: process.env.NODE_ENV,
      isDevelopment: ENV_CONFIG.isDevelopment,
      cookieConfig,
      timestamp: new Date().toISOString(),
    });

    response.cookie('auth_token', token, cookieConfig);
  }

  async login(user: User, response: Response) {
    // 更新最后登录时间
    user.lastLoginAt = new Date();
    await this.userService.update(user.id, {});

    const token = await this.generateToken(user);

    // 设置 cookie
    this.setCookies(response, token);

    // 只返回用户信息
    return {
      user: this.formatUserResponse(user),
    };
  }

  async register(registerDto: RegisterDto, response: Response) {
    // 检查邮箱是否已被注册
    const existingUser = await this.userService.findByEmail(registerDto.email);
    if (existingUser) {
      throw new ConflictException('该邮箱已被注册');
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    const user = await this.userService.create({
      ...registerDto,
      password: hashedPassword,
      role: UserRole.USER,
    });

    const token = await this.generateToken(user);

    // 设置 cookie
    this.setCookies(response, token);

    // 只返回用户信息
    return {
      user: this.formatUserResponse(user),
    };
  }

  async validateUser(emailOrId: string, password?: string): Promise<any> {
    const user = password
      ? await this.userService.findByEmail(emailOrId)
      : await this.userService.findById(emailOrId);

    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    if (user.status !== UserStatus.ACTIVE) {
      throw new UnauthorizedException('账号未激活或已被禁用');
    }

    if (password) {
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new UnauthorizedException('密码错误');
      }
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      status: user.status,
    };
  }

  async refreshToken(userId: string, response: Response) {
    const user = await this.validateUser(userId);
    const token = await this.generateToken(user);

    // 设置 cookie
    this.setCookies(response, token);

    // 只返回用户信息
    return {
      user: this.formatUserResponse(user),
    };
  }

  private async generateToken(user: any): Promise<string> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      role: user.role,
    };
    return this.jwtService.sign(payload);
  }

  private validateRole(user: User, role: string): boolean {
    const isValid = Object.values(UserRole).includes(role as UserRole);

    if (!isValid) {
      this.logger.error('Invalid role detected', {
        userId: user.id,
        email: user.email,
        invalidRole: role,
        timestamp: new Date().toISOString()
      });

      // 记录审计日志
      this.logRoleValidationFailure(user, role);

      return false;
    }

    // 如果是管理员，进行额外的验证
    if (role === UserRole.ADMIN) {
      const isValidAdmin = user.status === UserStatus.ACTIVE;

      if (!isValidAdmin) {
        this.logger.error('Admin validation failed', {
          userId: user.id,
          email: user.email,
          status: user.status,
          timestamp: new Date().toISOString()
        });

        this.notifyAdminValidationFailure(user);
        return false;
      }
    }

    return true;
  }

  private async logRoleValidationFailure(user: User, invalidRole: string): Promise<void> {
    try {
      this.logger.warn('Role validation failure logged', {
        userId: user.id,
        email: user.email,
        invalidRole,
        timestamp: new Date().toISOString(),
        status: user.status
      });

      // 这里可以添加持久化日志的逻辑
      // await this.auditLogService.log({ ... });

    } catch (error) {
      this.logger.error('Failed to log role validation failure', {
        error,
        userId: user.id,
        timestamp: new Date().toISOString()
      });
    }
  }

  private async notifyAdminValidationFailure(user: User): Promise<void> {
    try {
      this.logger.warn('Admin validation failure notification sent', {
        userId: user.id,
        email: user.email,
        status: user.status,
        timestamp: new Date().toISOString()
      });

      // 这里可以添加发送通知的逻辑
      // await this.notificationService.notifyAdmins({ ... });

    } catch (error) {
      this.logger.error('Failed to send admin validation notification', {
        error,
        userId: user.id,
        timestamp: new Date().toISOString()
      });
    }
  }

  public formatUserResponse(user: User) {
    // 进行角色验证
    const isValidRole = this.validateRole(user, user.role);

    if (!isValidRole) {
      // 如果角色无效，抛出异常而不是静默降级
      throw new BadRequestException({
        message: 'Invalid user role detected',
        userId: user.id,
        role: user.role
      });
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      role: user.role,
      status: user.status,
      isActive: user.status === UserStatus.ACTIVE,
      isAdmin: user.role === UserRole.ADMIN,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  async handleRoleValidationFailure(user: User): Promise<void> {
    try {
      this.logger.warn('Attempting to handle role validation failure', {
        userId: user.id,
        email: user.email,
        currentRole: user.role,
        timestamp: new Date().toISOString()
      });

      // 获取用户的完整历史记录
      const userHistory = await this.userService.getUserHistory(user.id);

      // 检查是否之前是管理员
      const wasAdmin = userHistory.some(record => record.role === UserRole.ADMIN);

      if (wasAdmin) {
        this.logger.warn('User was previously an admin, attempting to restore admin status', {
          userId: user.id,
          email: user.email,
          timestamp: new Date().toISOString()
        });

        // 尝试恢复管理员状态
        await this.userService.updateRole(user.id, UserRole.ADMIN);

        // 通知系统管理员
        await this.notifyAdminRoleRestored(user);
      }

    } catch (error) {
      this.logger.error('Failed to handle role validation failure', {
        error,
        userId: user.id,
        timestamp: new Date().toISOString()
      });

      // 重新抛出错误以便上层处理
      throw error;
    }
  }

  private async notifyAdminRoleRestored(user: User): Promise<void> {
    this.logger.log('Admin role restored notification sent', {
      userId: user.id,
      email: user.email,
      timestamp: new Date().toISOString()
    });

    // 这里可以添加发送通知的逻辑
    // await this.notificationService.notifyAdmins({ ... });
  }

  async setAdmin(email: string): Promise<User> {
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (!this.validateRole(user, UserRole.ADMIN)) {
      throw new BadRequestException('Invalid role value');
    }

    user.role = UserRole.ADMIN;
    return this.userService.update(user.id, user);
  }
}