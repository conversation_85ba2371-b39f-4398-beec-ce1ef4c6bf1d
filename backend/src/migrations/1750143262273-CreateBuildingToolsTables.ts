import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateBuildingToolsTables1750143262273 implements MigrationInterface {
    name = 'CreateBuildingToolsTables1750143262273'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // 使用条件删除约束，避免表不存在时报错
        await queryRunner.query(`
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'refunds') THEN
                    ALTER TABLE "refunds" DROP CONSTRAINT IF EXISTS "FK_refunds_order";
                END IF;
                
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
                    ALTER TABLE "orders" DROP CONSTRAINT IF EXISTS "FK_orders_course_id";
                END IF;
                
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payments') THEN
                    ALTER TABLE "payments" DROP CONSTRAINT IF EXISTS "FK_payments_order";
                END IF;
                
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'gift_codes') THEN
                    ALTER TABLE "gift_codes" DROP CONSTRAINT IF EXISTS "FK_gift_codes_user";
                    ALTER TABLE "gift_codes" DROP CONSTRAINT IF EXISTS "fk_gift_codes_creator";
                END IF;
                
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'course_building_tools') THEN
                    ALTER TABLE "course_building_tools" DROP CONSTRAINT IF EXISTS "FK_451cd3cffdd173f25d885c1c388";
                    ALTER TABLE "course_building_tools" DROP CONSTRAINT IF EXISTS "FK_cca1ede1db0a840162c66443c74";
                END IF;
            END
            $$;
        `);
        await queryRunner.query(`DROP INDEX "public"."idx_user_course_unique"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_course_building_tools_tool"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_course_building_tools_course"`);
        await queryRunner.query(`ALTER TABLE "course_lessons" DROP CONSTRAINT "UQ_course_lesson_order"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "valid_days"`);
        await queryRunner.query(`ALTER TABLE "course_building_tools" ADD CONSTRAINT "PK_419e49d25c4d7fca6ab51b82b3f" PRIMARY KEY ("tool_id", "course_id")`);
        await queryRunner.query(`ALTER TABLE "building_tools" ALTER COLUMN "created_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "building_tools" ALTER COLUMN "updated_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TYPE "public"."courses_level_enum" RENAME TO "courses_level_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."course_level_type_enum" AS ENUM('综合基础', '专项进阶', '技术高阶')`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "level" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "level" TYPE "public"."course_level_type_enum" USING "level"::"text"::"public"."course_level_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."courses_level_enum_old"`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "lessons_count" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "is_published" SET DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "refunds" ADD CONSTRAINT "UQ_a42db6369017df60549539f5567" UNIQUE ("order_id")`);
        await queryRunner.query(`ALTER TABLE "refunds" DROP COLUMN "reason"`);
        await queryRunner.query(`ALTER TABLE "refunds" ADD "reason" character varying`);
        await queryRunner.query(`ALTER TABLE "payments" ADD CONSTRAINT "UQ_b2f7b823a21562eeca20e72b006" UNIQUE ("order_id")`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "type" character varying NOT NULL DEFAULT 'gift'`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "friend_name"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "friend_name" character varying`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "invite_reason"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "invite_reason" character varying`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "user_name"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "user_name" character varying`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "profession"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "profession" character varying`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "ai_experience"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "ai_experience" character varying`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "expectation"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "expectation" character varying`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "updated_at" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`CREATE INDEX "IDX_451cd3cffdd173f25d885c1c38" ON "course_building_tools" ("tool_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_cca1ede1db0a840162c66443c7" ON "course_building_tools" ("course_id") `);
        await queryRunner.query(`ALTER TABLE "course_lessons" ADD CONSTRAINT "UQ_7868453c152529dfd47ee7cba34" UNIQUE ("course_id", "order")`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_517dc5f2b00bfd0406140400893" FOREIGN KEY ("userid") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "refunds" ADD CONSTRAINT "FK_a42db6369017df60549539f5567" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_a922b820eeef29ac1c6800e826a" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_8f64e2f0728bad0f6c6aa6413b2" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payments" ADD CONSTRAINT "FK_b2f7b823a21562eeca20e72b006" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "FK_f2d4a908c2d62d854c7770331e1" FOREIGN KEY ("used_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "FK_56b7d7f1847ef04c07c050adcea" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "course_building_tools" ADD CONSTRAINT "FK_451cd3cffdd173f25d885c1c388" FOREIGN KEY ("tool_id") REFERENCES "building_tools"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "course_building_tools" ADD CONSTRAINT "FK_cca1ede1db0a840162c66443c74" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        
        // 插入默认建站工具数据（如果不存在）
        await queryRunner.query(`
            INSERT INTO building_tools (code, title, description, icon, url, category, difficulty, "estimatedTime", "sortOrder") 
            VALUES
            ('landing-page-builder', 'AI落地页生成器', '5分钟制作专业落地页，支持产品推广、活动营销等多种场景', 'Zap', '/interactive-course', '页面制作', '初级', '5分钟', 1),
            ('seo-optimizer', 'SEO优化工具', '智能分析网站SEO表现，提供优化建议和关键词策略', 'Wrench', '#', 'SEO优化', '中级', '10分钟', 2),
            ('responsive-checker', '响应式设计检查', '一键检查网站在不同设备上的显示效果，确保完美适配', 'Wrench', '#', '设计优化', '初级', '3分钟', 3)
            ON CONFLICT (code) DO NOTHING
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "course_building_tools" DROP CONSTRAINT "FK_cca1ede1db0a840162c66443c74"`);
        await queryRunner.query(`ALTER TABLE "course_building_tools" DROP CONSTRAINT "FK_451cd3cffdd173f25d885c1c388"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT "FK_56b7d7f1847ef04c07c050adcea"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP CONSTRAINT "FK_f2d4a908c2d62d854c7770331e1"`);
        await queryRunner.query(`ALTER TABLE "payments" DROP CONSTRAINT "FK_b2f7b823a21562eeca20e72b006"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_8f64e2f0728bad0f6c6aa6413b2"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_a922b820eeef29ac1c6800e826a"`);
        await queryRunner.query(`ALTER TABLE "refunds" DROP CONSTRAINT "FK_a42db6369017df60549539f5567"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_517dc5f2b00bfd0406140400893"`);
        await queryRunner.query(`ALTER TABLE "course_lessons" DROP CONSTRAINT "UQ_7868453c152529dfd47ee7cba34"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cca1ede1db0a840162c66443c7"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_451cd3cffdd173f25d885c1c38"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "updated_at"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "expectation"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "expectation" text`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "ai_experience"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "ai_experience" character varying(50)`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "profession"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "profession" character varying(100)`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "user_name"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "user_name" character varying(100)`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "invite_reason"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "invite_reason" text`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "friend_name"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "friend_name" character varying(100)`);
        await queryRunner.query(`ALTER TABLE "gift_codes" DROP COLUMN "type"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "type" character varying(10) NOT NULL DEFAULT 'gift'`);
        await queryRunner.query(`ALTER TABLE "payments" DROP CONSTRAINT "UQ_b2f7b823a21562eeca20e72b006"`);
        await queryRunner.query(`ALTER TABLE "refunds" DROP COLUMN "reason"`);
        await queryRunner.query(`ALTER TABLE "refunds" ADD "reason" text`);
        await queryRunner.query(`ALTER TABLE "refunds" DROP CONSTRAINT "UQ_a42db6369017df60549539f5567"`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "is_published" SET DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "lessons_count" DROP DEFAULT`);
        await queryRunner.query(`CREATE TYPE "public"."courses_level_enum_old" AS ENUM('综合基础', '专项进阶', '技术高阶')`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "level" TYPE "public"."courses_level_enum_old" USING "level"::"text"::"public"."courses_level_enum_old"`);
        await queryRunner.query(`ALTER TABLE "courses" ALTER COLUMN "level" SET DEFAULT '综合基础'`);
        await queryRunner.query(`DROP TYPE "public"."course_level_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."courses_level_enum_old" RENAME TO "courses_level_enum"`);
        await queryRunner.query(`ALTER TABLE "building_tools" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "building_tools" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "course_building_tools" DROP CONSTRAINT "PK_419e49d25c4d7fca6ab51b82b3f"`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD "valid_days" integer NOT NULL`);
        await queryRunner.query(`ALTER TABLE "course_lessons" ADD CONSTRAINT "UQ_course_lesson_order" UNIQUE ("course_id", "order")`);
        await queryRunner.query(`CREATE INDEX "IDX_course_building_tools_course" ON "course_building_tools" ("course_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_course_building_tools_tool" ON "course_building_tools" ("tool_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "idx_user_course_unique" ON "user_courses" ("user_id", "course_id") `);
        await queryRunner.query(`ALTER TABLE "course_building_tools" ADD CONSTRAINT "FK_cca1ede1db0a840162c66443c74" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "course_building_tools" ADD CONSTRAINT "FK_451cd3cffdd173f25d885c1c388" FOREIGN KEY ("tool_id") REFERENCES "building_tools"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "fk_gift_codes_creator" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "gift_codes" ADD CONSTRAINT "FK_gift_codes_user" FOREIGN KEY ("used_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payments" ADD CONSTRAINT "FK_payments_order" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_course_id" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "refunds" ADD CONSTRAINT "FK_refunds_order" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
