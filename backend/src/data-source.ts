import { DataSource } from 'typeorm';
import { join } from 'path';
import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';

// 加载环境变量
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.development';
dotenv.config({ path: join(__dirname, '..', envFile) });

// 创建 ConfigService 实例
const configService = new ConfigService();

// 数据源配置
export const AppDataSource = new DataSource({
  type: 'postgres',
  host: configService.get('DB_HOST', 'localhost'),
  port: configService.get<number>('DB_PORT', 5432),
  username: configService.get('DB_USERNAME', 'seuzhy'),
  password: configService.get('DB_PASSWORD', 'seuzhy161230'),
  database: configService.get('DB_DATABASE', 'landingpages'),
  entities: [join(__dirname, './modules/**/*.entity{.ts,.js}')],
  migrations: [join(__dirname, './migrations/*{.ts,.js}')],
  synchronize: false,
  logging: configService.get('DB_LOGGING') === 'true',
}); 