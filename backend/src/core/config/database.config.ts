import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { join } from 'path';

export const getDatabaseConfig = (
  configService: ConfigService
): TypeOrmModuleOptions => ({
  type: 'postgres',
  host: configService.get('DB_HOST', 'localhost'),
  port: configService.get('DB_PORT', 5432),
  username: configService.get('DB_USERNAME', 'seuzhy'),
  password: configService.get('DB_PASSWORD', 'seuzhy161230'),
  database: configService.get('DB_DATABASE', 'landingpages'),
  entities: [join(__dirname, '../../modules/**/*.entity{.ts,.js}')],
  migrations: [join(__dirname, '../../database/migrations/*{.ts,.js}')],
  synchronize: false,
  logging: configService.get('DB_LOGGING', false),
  ssl: configService.get('DB_SSL', false)
    ? {
        rejectUnauthorized: false,
      }
    : false,
  migrationsRun: false,
  // 连接池配置
  poolSize: configService.get('DB_POOL_SIZE', 10),
  connectTimeoutMS: 0,
  extra: {
    // 最大连接数
    max: configService.get('DB_POOL_MAX', 20),
    // 空闲连接超时时间（毫秒）
    idleTimeoutMillis: configService.get('DB_POOL_IDLE_TIMEOUT', 30000),
  },
});

// 数据库迁移配置
export const getMigrationConfig = (
  configService: ConfigService
): TypeOrmModuleOptions => ({
  ...getDatabaseConfig(configService),
  migrations: [join(__dirname, '../../database/migrations/*{.ts,.js}')],
  migrationsTableName: 'migrations',
  migrationsRun: false,
}); 