# App
PORT=3001
NODE_ENV=production

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=seuzhy
DB_PASSWORD=seuzhy161230
DB_DATABASE=landingpages
DB_SYNCHRONIZE=false
DB_LOGGING=false
# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# Cache
CACHE_TTL=300
CACHE_MAX=1000

# JWT
JWT_SECRET=ai_learning_jwt_secret_2024
JWT_EXPIRATION=3600
JWT_REFRESH_SECRET=ai_learning_jwt_refresh_secret_2024
JWT_REFRESH_EXPIRES_IN=30d

# CORS
CORS_ORIGIN=http://**************

# Admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=PPOf3vcxvcLGB0etr9wA1w==
ADMIN_NAME=System Admin

# 日志配置
LOG_LEVEL=warn
LOG_FILE_PATH=/var/log/aiquicksite/app.log

# PostgreSQL environment variables
POSTGRES_USER=seuzhy
POSTGRES_PASSWORD=seuzhy161230
POSTGRES_DB=landingpages

# 邮件服务配置
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASSWORD=your_password
MAIL_SECURE=false
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME=AI探索家


# Cookie and CORS

COOKIE_DOMAIN=.aiquicksite.com
FRONTEND_URL=https://www.aiquicksite.com

# strip 設置
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_PUBLISHABLE_KEY=pk_live_51RHh9FKV3OLSrdJCL6WCLS9KGmdfCyTsxneEnUY4DIzlQpdJbcZegPDp0uLrzvaSTc7Hfjy4aboIoFeUVbCuVRiU00AyEZjMAy
STRIPE_WEBHOOK_SECRET=whsec_QWLhYHVCIJeqoG2th9Yxs1iOOMPdd0jy