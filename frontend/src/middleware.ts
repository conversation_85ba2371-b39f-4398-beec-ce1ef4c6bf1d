import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { jwtDecode } from 'jwt-decode'

// 环境配置
const ENV = {
  COOKIE_NAME: 'auth_token',
  PRODUCTION: process.env.NODE_ENV === 'production',
  API_DOMAIN: process.env.NEXT_PUBLIC_API_DOMAIN,
  FRONTEND_DOMAIN: process.env.NEXT_PUBLIC_FRONTEND_DOMAIN,
}

// 从请求中获取 token
function getTokenFromRequest(request: NextRequest): string | null {
  try {
    const token = request.cookies.get(ENV.COOKIE_NAME)?.value
    return token || null
  } catch (error) {
    console.error('[Middleware] Token 获取失败', {
      error,
      timestamp: new Date().toISOString()
    })
    return null
  }
}

// 验证 token
function isTokenValid(token: string | null): boolean {
  if (!token) return false

  try {
    const payload = jwtDecode<{ exp: number }>(token)
    return payload.exp * 1000 > Date.now()
  } catch (error) {
    console.error('[Middleware] Token 验证失败', {
      error,
      timestamp: new Date().toISOString()
    })
    return false
  }
}

// 创建重定向响应
function createRedirectResponse(request: NextRequest, path: string): NextResponse {
  const url = new URL(path, ENV.FRONTEND_DOMAIN)
  const response = NextResponse.redirect(url)

  // 设置安全头
  if (ENV.PRODUCTION) {
    // 基本安全头
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    
    // 额外的安全头
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('X-DNS-Prefetch-Control', 'on')
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
  }

  return response
}

// 公开路由列表
const PUBLIC_ROUTES = new Set([
  '/',
  '/login',
  '/register',
  '/admin/login',
  '/403',
  '/404',
  '/500',
  '/terms',
  '/privacy',
  '/all-courses',
  '/all-courses/*',
  '/interactive-course',  // Landing Page制作工具
])

// 静态资源路径
const STATIC_PATHS = [
  '/_next',
  '/static',
  '/images',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
]

// 检查是否是公开路由
function isPublicRoute(path: string): boolean {
  // 检查完整路径匹配
  if (PUBLIC_ROUTES.has(path)) return true

  // 检查路径前缀匹配
  return Array.from(PUBLIC_ROUTES).some(route => {
    if (route.endsWith('/*')) {
      const prefix = route.slice(0, -2)
      return path.startsWith(prefix)
    }
    return false
  })
}

// 检查是否是静态资源
function isStaticPath(path: string): boolean {
  return STATIC_PATHS.some(staticPath => path.startsWith(staticPath))
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 如果是静态资源，直接放行
  if (isStaticPath(pathname)) {
    return NextResponse.next()
  }

  // 如果是公开路由，直接放行
  if (isPublicRoute(pathname)) {
    return NextResponse.next()
  }

  // 获取并验证 token
  const token = getTokenFromRequest(request)
  const isValid = isTokenValid(token)

  // 如果是登录页且有无效 token，删除 cookie 并继续访问
  if ((pathname === '/login' || pathname === '/admin/login') && !isValid && token) {
    const response = NextResponse.next()
    response.cookies.delete(ENV.COOKIE_NAME)
    return response
  }

  // 如果 token 无效且不是公开路由，重定向到登录页
  if (!isValid) {
    const returnUrl = encodeURIComponent(pathname)
    const loginPath = pathname.startsWith('/admin')
      ? `/admin/login?returnUrl=${returnUrl}`
      : `/login?returnUrl=${returnUrl}`
    return createRedirectResponse(request, loginPath)
  }

  // 如果 token 有效，继续访问
  return NextResponse.next()
}

// 配置需要进行中间件处理的路由
export const config = {
  matcher: [
    /*
     * 匹配所有需要处理的路由:
     * - 需要认证的路由 (/dashboard/*)
     * - 登录注册相关路由 (/login, /register)
     * - 不匹配静态资源和API路由
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|ico)$).*)',
  ],
}