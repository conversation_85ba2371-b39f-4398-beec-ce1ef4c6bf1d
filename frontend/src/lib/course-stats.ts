// 简单的本地统计功能
export interface CourseStats {
  totalStarted: number
  totalCompleted: number
  completionRate: number
}

// 模拟的基础数据
const BASE_STATS = {
  totalStarted: 2847,
  totalCompleted: 1924,
  completionRate: 67.5
}

export function getCourseStats(): CourseStats {
  // 从localStorage获取本地统计
  const localStats = typeof window !== 'undefined' 
    ? localStorage.getItem('course-local-stats')
    : null
  
  if (localStats) {
    const parsed = JSON.parse(localStats)
    return {
      totalStarted: BASE_STATS.totalStarted + parsed.started,
      totalCompleted: BASE_STATS.totalCompleted + parsed.completed,
      completionRate: Math.round(((BASE_STATS.totalCompleted + parsed.completed) / (BASE_STATS.totalStarted + parsed.started)) * 100)
    }
  }
  
  return BASE_STATS
}

export function incrementCourseStarted() {
  if (typeof window === 'undefined') return
  
  const hasStarted = localStorage.getItem('user-started-course')
  if (!hasStarted) {
    localStorage.setItem('user-started-course', 'true')
    
    const stats = localStorage.getItem('course-local-stats')
    const current = stats ? JSON.parse(stats) : { started: 0, completed: 0 }
    current.started += 1
    localStorage.setItem('course-local-stats', JSON.stringify(current))
  }
}

export function incrementCourseCompleted() {
  if (typeof window === 'undefined') return
  
  const hasCompleted = localStorage.getItem('user-completed-course')
  if (!hasCompleted) {
    localStorage.setItem('user-completed-course', 'true')
    
    const stats = localStorage.getItem('course-local-stats')
    const current = stats ? JSON.parse(stats) : { started: 0, completed: 0 }
    current.completed += 1
    localStorage.setItem('course-local-stats', JSON.stringify(current))
  }
}

export function formatNumber(num: number): string {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
} 