import { api } from '@/lib/api/index';

export interface Category {
  id: string;
  name: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CreateCategoryDto {
  name: string;
  description?: string;
}

export interface UpdateCategoryDto {
  name?: string;
  description?: string;
}

export class CategoryService {
  static async getCategories(): Promise<Category[]> {
    const response = await api.get('/api/admin/categories');
    return response.data;
  }

  static async getCategory(id: string): Promise<Category> {
    const response = await api.get(`/api/admin/categories/${id}`);
    return response.data;
  }

  static async createCategory(data: CreateCategoryDto): Promise<Category> {
    const response = await api.post('/api/admin/categories', data);
    return response.data;
  }

  static async updateCategory(id: string, data: UpdateCategoryDto): Promise<Category> {
    const response = await api.patch(`/api/admin/categories/${id}`, data);
    return response.data;
  }

  static async deleteCategory(id: string): Promise<void> {
    await api.delete(`/api/admin/categories/${id}`);
  }
} 