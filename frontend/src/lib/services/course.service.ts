import { api } from '@/lib/api/index';

export interface Course {
  id: string;
  code: string;
  title: string;
  description: string;
  level: '综合基础' | '专项进阶' | '技术高阶';
  duration: string;
  lessonsCount: number;
  price: number;
  isPublished: boolean;
  isPurchased?: boolean;
  progress?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CourseLesson {
  id: string;
  courseId: string;
  title: string;
  order: number;
  duration: string;
  description: string;
  scriptPath: string;
  isCompleted?: boolean;
}

export interface BuildingTool {
  id: string;
  code: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  category: string;
  difficulty: string;
  estimatedTime: string;
  isActive: boolean;
  sortOrder: number;
}

export class CourseService {
  /**
   * 获取课程列表
   * @param includeUnpublished 是否包含未发布的课程（仅管理员可用）
   */
  static async getCourses(includeUnpublished = false): Promise<Course[]> {
    const response = await api.get('/api/courses', {
      params: { includeUnpublished },
      withCredentials: true,
    });
    return response.data;
  }

  static async getCourse(id: string): Promise<Course> {
    const response = await api.get(`/api/courses/${id}`, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 获取课程章节列表（带学习进度）
   * 需要用户登录
   */
  static async getCourseLessons(courseId: string): Promise<CourseLesson[]> {
    const response = await api.get(`/api/courses/${courseId}/lessons`, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 获取课程章节列表（公开）
   * 不需要用户登录
   */
  static async getCoursePublicLessons(courseId: string): Promise<CourseLesson[]> {
    const response = await api.get(`/api/courses/${courseId}/public-lessons`, {
      withCredentials: true,
    });
    return response.data;
  }

  static async updateProgress(courseId: string, lessonId: string): Promise<void> {
    await api.post(`/api/courses/${courseId}/lessons/${lessonId}/progress`);
  }

  static async getProgress(courseId: string): Promise<{
    progress: number;
    completedLessons: string[];
    lastVisitedLessonId: string;
  }> {
    const response = await api.get(`/api/courses/${courseId}/progress`);
    return response.data;
  }

  /**
   * 获取课程章节脚本内容
   */
  static async getLessonContent(courseId: string, lessonId: string): Promise<string> {
    const response = await api.get(`/api/courses/${courseId}/lessons/${lessonId}/content`);
    return response.data.content;
  }

  /**
   * 更新课程章节脚本内容
   */
  static async updateLessonContent(courseId: string, lessonId: string, content: string): Promise<void> {
    await api.put(
      `/api/courses/${courseId}/lessons/${lessonId}/content`,
      { content },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // 检查用户是否已购买课程
  static async checkPurchaseStatus(courseId: string): Promise<boolean> {
    try {
      const response = await api.get(`/api/courses/${courseId}/lessons`);
      return true;
    } catch (error: any) {
      if (error.response?.status === 404 && error.response?.data?.message === '请先购买课程') {
        return false;
      }
      throw error;
    }
  }

  // 获取用户已购买的课程
  static async getPurchasedCourses(): Promise<Course[]> {
    const response = await api.get('/api/courses/purchased');
    return response.data;
  }

  // 加入免费课程
  static async enrollFreeCourse(courseId: string): Promise<{ courseId: string; message: string }> {
    const response = await api.post(`/api/courses/free-enroll/${courseId}`);
    return response.data;
  }

  /**
   * 删除课程
   */
  static async deleteCourse(id: string): Promise<void> {
    await api.delete(`/api/courses/${id}`);
  }

  /**
   * 创建课程
   */
  static async createCourse(courseData: Partial<Course>): Promise<Course> {
    const { data } = await api.post('/api/courses', courseData);
    return data;
  }

  /**
   * 更新课程
   */
  static async updateCourse(id: string, courseData: Partial<Course>): Promise<Course> {
    const { data } = await api.put(`/api/courses/${id}`, courseData);
    return data;
  }

  /**
   * 发布课程
   */
  static async publishCourse(id: string): Promise<Course> {
    const { data } = await api.post(`/api/courses/${id}/publish`);
    return data;
  }

  /**
   * 取消发布课程
   */
  static async unpublishCourse(id: string): Promise<Course> {
    const { data } = await api.post(`/api/courses/${id}/unpublish`);
    return data;
  }

  /**
   * 切换课程发布状态
   */
  static async togglePublishCourse(id: string): Promise<Course> {
    const { data } = await api.post(`/api/courses/${id}/toggle-publish`);
    return data;
  }

  /**
   * 创建课程章节
   */
  static async createLesson(courseId: string, lessonData: Partial<CourseLesson>): Promise<CourseLesson> {
    const { data } = await api.post(`/api/courses/${courseId}/lessons`, lessonData);
    return data;
  }

  /**
   * 更新课程章节
   */
  static async updateLesson(
    courseId: string,
    lessonId: string,
    lessonData: Partial<CourseLesson>
  ): Promise<CourseLesson> {
    const { data } = await api.patch(
      `/api/courses/${courseId}/lessons/${lessonId}`,
      lessonData
    );
    return data;
  }

  /**
   * 删除课程章节
   */
  static async deleteLesson(courseId: string, lessonId: string): Promise<void> {
    await api.delete(`/api/courses/${courseId}/lessons/${lessonId}`);
  }

  /**
   * 重新排序课程章节
   */
  static async reorderLessons(courseId: string, lessonIds: string[]): Promise<void> {
    await api.post(`/api/courses/${courseId}/lessons/reorder`, { lessonIds });
  }

  /**
   * 获取课程的建站工具
   */
  static async getCourseBuildingTools(courseId: string): Promise<BuildingTool[]> {
    const response = await api.get(`/api/courses/${courseId}/building-tools`, {
      withCredentials: true,
    });
    return response.data;
  }

  /**
   * 获取课程工具（别名方法，保持一致性）
   */
  static async getCourseTools(courseId: string): Promise<BuildingTool[]> {
    return this.getCourseBuildingTools(courseId);
  }

  /**
   * 更新课程的建站工具配置
   */
  static async updateCourseTools(courseId: string, toolIds: string[]): Promise<void> {
    await api.put(`/api/courses/${courseId}/building-tools`, {
      toolIds,
    });
  }
} 