// Landing Page制作工具统计信息管理

interface LandingPageStats {
  totalGenerated: number // 总生成数量
  totalDownloaded: number // 总下载数量
  todayGenerated: number // 今日生成数量
  activeUsers: number // 活跃用户数
}

const STATS_KEY = 'landing-page-builder-stats'
const USER_SESSION_KEY = 'landing-page-builder-session'

// 默认统计数据
const defaultStats: LandingPageStats = {
  totalGenerated: 1247,
  totalDownloaded: 892,
  todayGenerated: 23,
  activeUsers: 156
}

// 获取统计信息 - 修复SSR水合问题
export function getLandingPageStats(): LandingPageStats {
  // 服务器端始终返回默认值，避免水合错误
  if (typeof window === 'undefined') {
    return defaultStats
  }

  try {
    const saved = localStorage.getItem(STATS_KEY)
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.error('读取统计数据失败:', error)
  }

  // 如果没有保存的数据或读取失败，保存默认数据
  try {
    localStorage.setItem(STATS_KEY, JSON.stringify(defaultStats))
  } catch (error) {
    console.error('保存统计数据失败:', error)
  }
  
  return defaultStats
}

// 增加生成次数
export function incrementGenerated() {
  if (typeof window === 'undefined') return
  
  try {
    const stats = getLandingPageStats()
    stats.totalGenerated += 1
    stats.todayGenerated += 1
    
    // 标记用户会话
    if (!localStorage.getItem(USER_SESSION_KEY)) {
      localStorage.setItem(USER_SESSION_KEY, Date.now().toString())
      stats.activeUsers += 1
    }
    
    localStorage.setItem(STATS_KEY, JSON.stringify(stats))
  } catch (error) {
    console.error('更新生成统计失败:', error)
  }
}

// 增加下载次数
export function incrementDownloaded() {
  if (typeof window === 'undefined') return
  
  try {
    const stats = getLandingPageStats()
    stats.totalDownloaded += 1
    localStorage.setItem(STATS_KEY, JSON.stringify(stats))
  } catch (error) {
    console.error('更新下载统计失败:', error)
  }
}

// 格式化数字显示
export function formatNumber(num: number): string {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 获取今日时间标识
function getTodayKey(): string {
  return new Date().toDateString()
}

// 重置今日统计（可以在定时任务中调用）
export function resetTodayStats() {
  if (typeof window === 'undefined') return
  
  try {
    const stats = getLandingPageStats()
    const lastResetKey = localStorage.getItem('last-reset-date')
    const todayKey = getTodayKey()
    
    if (lastResetKey !== todayKey) {
      stats.todayGenerated = 0
      localStorage.setItem(STATS_KEY, JSON.stringify(stats))
      localStorage.setItem('last-reset-date', todayKey)
    }
  } catch (error) {
    console.error('重置今日统计失败:', error)
  }
} 