'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseLesson, CourseService, BuildingTool } from '@/lib/services/course.service';
import { Clock, BookOpen, CheckCircle, Zap, Wrench, ExternalLink } from 'lucide-react';
import Link from 'next/link';

// 图标映射
const iconMap: Record<string, React.ComponentType<any>> = {
  Zap,
  Wrench,
};

export default function CoursePage() {
  const params = useParams();
  const courseId = params.id as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [lessons, setLessons] = useState<CourseLesson[]>([]);
  const [buildingTools, setBuildingTools] = useState<BuildingTool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'tools' | 'lessons'>('tools');

  useEffect(() => {
    const fetchCourseData = async () => {
      try {
        const [courseData, lessonsData, buildingToolsData] = await Promise.all([
          CourseService.getCourse(courseId),
          CourseService.getCourseLessons(courseId),
          CourseService.getCourseBuildingTools(courseId),
        ]);
        
        setCourse(courseData);
        setLessons(lessonsData);
        setBuildingTools(buildingToolsData);
      } catch (err) {
        setError('加载课程信息失败，请稍后重试');
        console.error('Failed to fetch course:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [courseId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || '课程不存在'}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 课程头部信息 */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Badge variant="secondary">{course.level}</Badge>
            <Badge variant="default" className="bg-blue-500">
              已购买
            </Badge>
          </div>
          <h1 className="text-3xl font-bold mb-4">{course.title}</h1>
          <p className="text-lg text-muted-foreground mb-6">{course.description}</p>
          <div className="flex items-center gap-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span>{course.duration}</span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <span>{course.lessonsCount} 课时</span>
            </div>
          </div>
        </div>

        {/* 标签切换 */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <div className="flex space-x-8">
              <button
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'tools'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('tools')}
              >
                <div className="flex items-center gap-2">
                  <Wrench className="h-4 w-4" />
                  建站工具
                </div>
              </button>
              <button
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'lessons'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('lessons')}
              >
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  课程章节
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* 建站工具部分 */}
        {activeTab === 'tools' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold">建站工具</h2>
              <p className="text-muted-foreground">实践出真知，边学边做更高效</p>
            </div>
            
            <div className="grid gap-4">
              {buildingTools.map((tool) => {
                const IconComponent = iconMap[tool.icon] || Wrench;
                return (
                  <Card key={tool.id} className="p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <IconComponent className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium">{tool.title}</h3>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                              <span className="bg-gray-100 px-2 py-1 rounded text-xs">{tool.category}</span>
                              <span>{tool.difficulty}</span>
                              <span>约 {tool.estimatedTime}</span>
                            </div>
                          </div>
                        </div>
                        <p className="text-muted-foreground mb-4">{tool.description}</p>
                      </div>
                      <div className="ml-4">
                        {tool.url === '#' ? (
                          <Button variant="outline" disabled className="cursor-not-allowed">
                            即将推出
                          </Button>
                        ) : (
                          <Button asChild variant="outline">
                            <Link href={tool.url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                              开始使用
                              <ExternalLink className="h-4 w-4" />
                            </Link>
                          </Button>
                        )}
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            {/* 工具使用提示 */}
            <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">💡 学习建议</h4>
                  <p className="text-blue-800 text-sm">
                    建议先使用建站工具进行实践，在动手过程中发现问题，然后通过课程章节学习理论知识，这样学习效果更佳。
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 课程章节部分 */}
        {activeTab === 'lessons' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold">课程大纲</h2>
              <p className="text-muted-foreground">{lessons.length} 个章节</p>
            </div>
            
            {lessons.map((lesson) => (
              <Card key={lesson.id} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">第 {lesson.order} 章</span>
                      {lesson.isCompleted && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                    <h3 className="text-lg font-medium mt-1">{lesson.title}</h3>
                    <p className="text-muted-foreground mt-1">{lesson.description}</p>
                    <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>{lesson.duration}</span>
                    </div>
                  </div>
                  <Button asChild variant="outline">
                    <Link href={`/my-courses/${course.id}/lessons/${lesson.id}${lesson.isCompleted ? '?mode=review' : ''}`}>
                      {lesson.isCompleted ? (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          复习
                        </>
                      ) : (
                        '开始学习'
                      )}
                    </Link>
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 