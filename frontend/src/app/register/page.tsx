'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { RegisterForm } from '@/components/auth/register-form'
import { useAuth } from '@/auth/contexts/simplified-auth-context'
import { useToast } from '@/components/ui/use-toast'
import { LoadingWrapper } from '@/components/auth/loading-wrapper'

export default function RegisterPage() {
  const router = useRouter()
  const { isAuthenticated, isLoading, register } = useAuth()
  const { toast } = useToast()

  // 处理认证状态变化
  useEffect(() => {
    console.log('[RegisterPage] 认证状态检查', {
      isAuthenticated,
      isLoading,
      timestamp: new Date().toISOString()
    })

    // 注释掉重定向逻辑，允许已登录用户访问注册页面
    // if (!isLoading && isAuthenticated) {
    //   console.log('[RegisterPage] 已认证，重定向到仪表盘', {
    //     timestamp: new Date().toISOString()
    //   })
    //   router.push('/dashboard')
    // }
  }, [isAuthenticated, isLoading, router])

  // 处理注册提交
  const handleRegister = async (data: { name: string; email: string; password: string }) => {
    try {
      await register(data)
      toast({
        title: '注册成功',
        description: '欢迎加入 AI探索家！',
      })
    } catch (error) {
      console.error('[RegisterPage] 注册失败:', error)
      toast({
        title: '注册失败',
        description: error instanceof Error ? error.message : '发生未知错误',
        variant: 'destructive',
      })
      throw error // 向上传播错误以便表单组件处理
    }
  }

  // 使用 LoadingWrapper 处理加载状态
  return (
    <LoadingWrapper>
      <div className="container relative h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
        <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
          <div className="absolute inset-0 bg-zinc-900" />
          <div className="relative z-20 flex items-center text-lg font-medium">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2 h-6 w-6"
            >
              <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
            </svg>
            AI探索家
          </div>
          <div className="relative z-20 mt-auto">
            <blockquote className="space-y-2">
              <p className="text-lg">
                加入 AI探索家，开启您的AI学习之旅。
              </p>
            </blockquote>
          </div>
        </div>
        <div className="lg:p-8">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
            <div className="flex flex-col space-y-2 text-center">
              <h1 className="text-2xl font-semibold tracking-tight">
                创建账户
              </h1>
              <p className="text-sm text-muted-foreground">
                输入您的信息创建账户
              </p>
            </div>
            <RegisterForm onSubmit={handleRegister} />
          </div>
        </div>
      </div>
    </LoadingWrapper>
  )
}