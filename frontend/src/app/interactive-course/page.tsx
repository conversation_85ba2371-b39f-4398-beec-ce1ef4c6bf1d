'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  Zap, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft, 
  Sparkles, 
  Eye,
  Code,
  Download,
  Target,
  Users,
  Palette,
  Layout,
  MessageCircle,
  Rocket,
  FileText,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'
import { LandingPageBuilderNav } from '@/components/interactive-course/course-nav'
import { getLandingPageStats, incrementGenerated, incrementDownloaded, formatNumber } from '@/lib/landing-page-stats'

// Landing Page制作步骤
const builderSteps = [
  {
    id: 1,
    title: "项目需求收集",
    description: "告诉我们你的业务和目标",
    icon: Target,
    fields: ['business', 'target', 'goals']
  },
  {
    id: 2,
    title: "目标受众分析",
    description: "定义你的理想客户",
    icon: Users,
    fields: ['audience', 'painPoints', 'benefits']
  },
  {
    id: 3,
    title: "内容策划",
    description: "AI帮你生成核心内容",
    icon: MessageCircle,
    fields: ['headline', 'subheadline', 'cta', 'features']
  },
  {
    id: 4,
    title: "视觉设计",
    description: "选择配色和布局风格",
    icon: Palette,
    fields: ['colorScheme', 'layout', 'style']
  },
  {
    id: 5,
    title: "页面预览",
    description: "查看你的Landing Page效果",
    icon: Eye,
    fields: []
  },
  {
    id: 6,
    title: "代码交付",
    description: "获取完整的网页代码",
    icon: Code,
    fields: []
  }
]

interface FormData {
  // 步骤1: 项目需求
  business: string
  target: string
  goals: string
  
  // 步骤2: 目标受众
  audience: string
  painPoints: string
  benefits: string
  
  // 步骤3: 内容策划
  headline: string
  subheadline: string
  cta: string
  features: string[]
  
  // 步骤4: 视觉设计
  colorScheme: string
  layout: string
  style: string
}

const defaultFormData: FormData = {
  business: '',
  target: '',
  goals: '',
  audience: '',
  painPoints: '',
  benefits: '',
  headline: '',
  subheadline: '',
  cta: '',
  features: [],
  colorScheme: 'blue',
  layout: 'modern',
  style: 'professional'
}

export default function LandingPageBuilder() {
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [formData, setFormData] = useState<FormData>(defaultFormData)
  const [isGenerating, setIsGenerating] = useState(false)
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')
  const [generatedHtml, setGeneratedHtml] = useState('')
  const [stats, setStats] = useState(getLandingPageStats())
  const [isClient, setIsClient] = useState(false)

  // 从本地存储加载进度
  useEffect(() => {
    const savedProgress = localStorage.getItem('landing-page-builder-progress')
    if (savedProgress) {
      const { currentStep: saved, completed, data } = JSON.parse(savedProgress)
      setCurrentStep(saved)
      setCompletedSteps(completed)
      setFormData({ ...defaultFormData, ...data })
    }
    
    // 标记为客户端渲染，刷新统计信息
    setIsClient(true)
    setStats(getLandingPageStats())
  }, [])

  // 保存进度到本地存储
  useEffect(() => {
    localStorage.setItem('landing-page-builder-progress', JSON.stringify({
      currentStep,
      completed: completedSteps,
      data: formData
    }))
  }, [currentStep, completedSteps, formData])

  const progress = (completedSteps.length / builderSteps.length) * 100

  // AI内容生成函数
  const generateWithAI = async (type: string) => {
    setIsGenerating(true)
    try {
      let response
      
      switch (type) {
        case 'headline':
          response = await fetch('/api/ai/landing-page/generate-headline', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              business: formData.business,
              target: formData.target,
              goals: formData.goals,
              audience: formData.audience,
              painPoints: formData.painPoints
            })
          })
          
          if (response.ok) {
            const data = await response.json()
            setFormData(prev => ({
              ...prev,
              headline: data.headline,
              subheadline: data.subheadline
            }))
          } else {
            throw new Error('生成标题失败')
          }
          break
          
        case 'features':
          response = await fetch('/api/ai/landing-page/generate-features', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              business: formData.business,
              benefits: formData.benefits,
              audience: formData.audience
            })
          })
          
          if (response.ok) {
            const data = await response.json()
            setFormData(prev => ({
              ...prev,
              features: data.features
            }))
          } else {
            throw new Error('生成功能特色失败')
          }
          break
          
        case 'cta':
          response = await fetch('/api/ai/landing-page/generate-cta', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              business: formData.business,
              goals: formData.goals
            })
          })
          
          if (response.ok) {
            const data = await response.json()
            setFormData(prev => ({
              ...prev,
              cta: data.cta
            }))
          } else {
            throw new Error('生成CTA失败')
          }
          break
          
        case 'optimize':
          response = await fetch('/api/ai/landing-page/optimize-content', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              business: formData.business,
              target: formData.target,
              goals: formData.goals,
              audience: formData.audience,
              painPoints: formData.painPoints,
              benefits: formData.benefits
            })
          })
          
          if (response.ok) {
            const data = await response.json()
            setFormData(prev => ({
              ...prev,
              headline: data.headline,
              subheadline: data.subheadline,
              features: data.features,
              cta: data.cta
            }))
          } else {
            throw new Error('优化内容失败')
          }
          break
      }
    } catch (error) {
      console.error('AI生成失败:', error)
      // 如果API调用失败，使用默认值
      switch (type) {
        case 'headline':
          setFormData(prev => ({
            ...prev,
            headline: `${prev.business}专业解决方案 - 助您实现${prev.goals}`,
            subheadline: `为${prev.audience}量身定制，解决${prev.painPoints}的痛点`
          }))
          break
        case 'features':
          setFormData(prev => ({
            ...prev,
            features: [
              '专业可靠的服务',
              '快速响应支持',
              '性价比超高',
              '客户满意保证'
            ]
          }))
          break
        case 'cta':
          setFormData(prev => ({
            ...prev,
            cta: '立即开始'
          }))
          break
      }
    } finally {
      setIsGenerating(false)
    }
  }

  // 生成完整的Landing Page HTML
  const generateLandingPage = () => {
    const primaryColor = formData.colorScheme === 'blue' ? '#667eea' : 
                        formData.colorScheme === 'green' ? '#48bb78' :
                        formData.colorScheme === 'purple' ? '#9f7aea' :
                        formData.colorScheme === 'red' ? '#f56565' :
                        formData.colorScheme === 'orange' ? '#ed8936' :
                        formData.colorScheme === 'teal' ? '#38b2ac' : '#667eea'
    
    const secondaryColor = formData.colorScheme === 'blue' ? '#764ba2' : 
                          formData.colorScheme === 'green' ? '#38a169' :
                          formData.colorScheme === 'purple' ? '#805ad5' :
                          formData.colorScheme === 'red' ? '#e53e3e' :
                          formData.colorScheme === 'orange' ? '#dd6b20' :
                          formData.colorScheme === 'teal' ? '#319795' : '#764ba2'
    
    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${formData.headline}</title>
    <meta name="description" content="${formData.subheadline}">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif; 
            line-height: 1.6; 
            color: #333;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        
        /* Hero Section */
        .hero { 
            background: linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%); 
            color: white; 
            padding: 100px 0; 
            text-align: center; 
            min-height: 80vh;
            display: flex;
            align-items: center;
        }
        .hero h1 { 
            font-size: 3.5rem; 
            margin-bottom: 1.5rem; 
            font-weight: 700; 
            line-height: 1.2;
        }
        .hero p { 
            font-size: 1.5rem; 
            margin-bottom: 2.5rem; 
            opacity: 0.95; 
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Buttons */
        .cta-button { 
            display: inline-block; 
            background: #ff6b6b; 
            color: white; 
            padding: 18px 36px; 
            text-decoration: none; 
            border-radius: 12px; 
            font-weight: 600; 
            font-size: 1.3rem; 
            margin: 10px; 
            transition: all 0.3s ease; 
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        .cta-button:hover { 
            transform: translateY(-3px); 
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        /* Features Section */
        .features { 
            padding: 100px 0; 
            background: #f8f9fa; 
        }
        .features-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
            gap: 40px; 
            margin-top: 60px; 
        }
        .feature-card { 
            background: white; 
            padding: 40px 30px; 
            border-radius: 16px; 
            box-shadow: 0 8px 30px rgba(0,0,0,0.08); 
            text-align: center; 
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-card .icon {
            width: 60px;
            height: 60px;
            background: ${primaryColor};
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
            color: white;
        }
        .feature-card h3 { 
            color: #333; 
            margin-bottom: 15px; 
            font-size: 1.4rem;
            font-weight: 600;
        }
        .feature-card p {
            color: #667;
            line-height: 1.6;
        }
        
        /* Contact Section */
        .contact {
            padding: 100px 0;
            background: linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%);
            color: white;
        }
        .contact-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }
        .contact-form {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 16px;
        }
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        .submit-btn {
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        .submit-btn:hover {
            background: #ff5252;
        }
        
        /* Typography */
        .section-title { 
            text-align: center; 
            font-size: 2.8rem; 
            color: #333; 
            margin-bottom: 20px; 
            font-weight: 700;
        }
        .section-subtitle { 
            text-align: center; 
            font-size: 1.3rem; 
            color: #666; 
            margin-bottom: 50px; 
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h1 { font-size: 2.5rem; }
            .hero p { font-size: 1.2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .contact-content { grid-template-columns: 1fr; gap: 40px; }
            .container { padding: 0 15px; }
            .hero { padding: 60px 0; }
            .features { padding: 60px 0; }
            .contact { padding: 60px 0; }
        }
        
        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="fade-in-up">
                <h1>${formData.headline}</h1>
                <p>${formData.subheadline}</p>
                <a href="#contact" class="cta-button">${formData.cta}</a>
            </div>
        </div>
    </section>
    
    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">为什么选择我们？</h2>
            <p class="section-subtitle">${formData.benefits}</p>
            <div class="features-grid">
                ${formData.features.map((feature, index) => `
                    <div class="feature-card fade-in-up" style="animation-delay: ${index * 0.1}s">
                        <div class="icon">★</div>
                        <h3>${feature}</h3>
                        <p>专业的${feature}，为您提供最优质的服务体验。</p>
                    </div>
                `).join('')}
            </div>
        </div>
    </section>
    
    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="contact-content">
                <div>
                    <h2 style="font-size: 2.5rem; margin-bottom: 20px;">准备开始了吗？</h2>
                    <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">
                        立即联系我们，获取专业咨询和定制化解决方案。我们的专家团队将为您提供最优质的服务。
                    </p>
                    <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 12px;">
                        <h3 style="margin-bottom: 15px;">联系方式</h3>
                        <p style="margin-bottom: 10px;">📧 邮箱：<EMAIL></p>
                        <p style="margin-bottom: 10px;">📱 电话：400-123-4567</p>
                        <p>🕐 工作时间：周一至周五 9:00-18:00</p>
                    </div>
                </div>
                <div class="contact-form">
                    <h3 style="margin-bottom: 25px; font-size: 1.5rem;">获取免费咨询</h3>
                    <form>
                        <div class="form-group">
                            <label for="name">姓名 *</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">邮箱 *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">电话</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                        <div class="form-group">
                            <label for="message">需求描述</label>
                            <textarea id="message" name="message" placeholder="请描述您的具体需求..."></textarea>
                        </div>
                        <button type="submit" class="submit-btn">提交咨询</button>
                    </form>
                </div>
            </div>
        </div>
    </section>
    
    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
        
        // 表单提交处理
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('感谢您的咨询！我们会尽快与您联系。');
        });
    </script>
</body>
</html>`
    setGeneratedHtml(html)
    setStats(getLandingPageStats())
  }

  const handleNext = () => {
    if (currentStep < builderSteps.length) {
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps([...completedSteps, currentStep])
      }
      setCurrentStep(currentStep + 1)
      
      // 如果到了预览步骤，生成HTML
      if (currentStep + 1 === 5) {
        generateLandingPage()
        incrementGenerated()
        setStats(getLandingPageStats())
      }
    } else {
      // 到达最后一步，完成流程
      handleComplete()
    }
  }

  // 处理完成流程
  const handleComplete = () => {
    // 标记最后一步为完成
    if (!completedSteps.includes(currentStep)) {
      setCompletedSteps([...completedSteps, currentStep])
    }
    
    // 可以添加完成后的逻辑，比如：
    // - 显示成功提示
    // - 重置流程
    // - 跳转到其他页面
    // - 发送分析数据等
    
    alert('🎉 恭喜！你的Landing Page制作完成！\n\n你可以:\n• 下载HTML文件\n• 分享你的成果\n• 重新开始制作新的页面')
    
    // 可选：重置到第一步开始新的制作流程
    // setCurrentStep(1)
    // setCompletedSteps([])
    // setFormData(defaultFormData)
    // setGeneratedHtml('')
  }

  const handlePrev = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const currentStepData = builderSteps.find(step => step.id === currentStep)
  const StepIcon = currentStepData?.icon || Target

  // 下载HTML文件
  const downloadHtml = () => {
    const blob = new Blob([generatedHtml], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'landing-page.html'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    // 增加下载统计
    incrementDownloaded()
    setStats(getLandingPageStats())
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <LandingPageBuilderNav />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* 进度条 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900">快速完成你的Landing Page</h1>
            <Badge variant="outline" className="text-sm">
              步骤 {currentStep} / {builderSteps.length}
            </Badge>
          </div>
          <Progress value={progress} className="h-2" />
          
          {/* 步骤指示器 */}
          <div className="flex items-center justify-between mt-6">
            {builderSteps.map((step, index) => {
              const StepIconComponent = step.icon
              const isCompleted = completedSteps.includes(step.id)
              const isCurrent = currentStep === step.id
              
              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div className={`
                    w-12 h-12 rounded-full flex items-center justify-center mb-2 transition-colors
                    ${isCompleted ? 'bg-green-500 text-white' : 
                      isCurrent ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'}
                  `}>
                    {isCompleted ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <StepIconComponent className="h-6 w-6" />
                    )}
                  </div>
                  <span className={`text-xs text-center ${isCurrent ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                    {step.title}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：表单区域 */}
          <div>
            <Card className="p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center">
                  <StepIcon className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{currentStepData?.title}</h2>
                  <p className="text-gray-600">{currentStepData?.description}</p>
                </div>
              </div>

              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {/* 步骤1: 项目需求收集 */}
                  {currentStep === 1 && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="business">你的业务/产品是什么？</Label>
                        <Input
                          id="business"
                          value={formData.business}
                          onChange={(e) => setFormData(prev => ({ ...prev, business: e.target.value }))}
                          placeholder="例如：在线教育平台、SaaS软件、电商产品..."
                        />
                      </div>
                      <div>
                        <Label htmlFor="target">主要服务对象？</Label>
                        <Input
                          id="target"
                          value={formData.target}
                          onChange={(e) => setFormData(prev => ({ ...prev, target: e.target.value }))}
                          placeholder="例如：中小企业主、个人创业者、学生..."
                        />
                      </div>
                      <div>
                        <Label htmlFor="goals">希望通过Landing Page实现什么目标？</Label>
                        <Textarea
                          id="goals"
                          value={formData.goals}
                          onChange={(e) => setFormData(prev => ({ ...prev, goals: e.target.value }))}
                          placeholder="例如：获取潜在客户联系方式、提高产品注册率、增加销售转化..."
                          rows={3}
                        />
                      </div>
                    </div>
                  )}

                  {/* 步骤2: 目标受众分析 */}
                  {currentStep === 2 && (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="audience">详细描述你的目标客户</Label>
                        <Textarea
                          id="audience"
                          value={formData.audience}
                          onChange={(e) => setFormData(prev => ({ ...prev, audience: e.target.value }))}
                          placeholder="例如：25-45岁的企业管理者，希望提高工作效率，有一定的技术接受能力..."
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label htmlFor="painPoints">他们主要面临什么问题？</Label>
                        <Textarea
                          id="painPoints"
                          value={formData.painPoints}
                          onChange={(e) => setFormData(prev => ({ ...prev, painPoints: e.target.value }))}
                          placeholder="例如：工作效率低、成本高、缺乏专业工具..."
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label htmlFor="benefits">你的产品如何帮助他们？</Label>
                        <Textarea
                          id="benefits"
                          value={formData.benefits}
                          onChange={(e) => setFormData(prev => ({ ...prev, benefits: e.target.value }))}
                          placeholder="例如：提高50%工作效率、降低30%成本、提供专业解决方案..."
                          rows={3}
                        />
                      </div>
                    </div>
                  )}

                  {/* 步骤3: 内容策划 */}
                  {currentStep === 3 && (
                    <div className="space-y-4">
                      {/* 一键优化按钮 */}
                      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium text-purple-900">AI一键优化</h4>
                            <p className="text-sm text-purple-600">基于前面的信息，AI为您生成完整的内容方案</p>
                          </div>
                          <Button
                            onClick={() => generateWithAI('optimize')}
                            disabled={isGenerating || !formData.business || !formData.audience}
                            className="bg-purple-600 hover:bg-purple-700 flex items-center space-x-2"
                          >
                            <Sparkles className="h-4 w-4" />
                            <span>{isGenerating ? '优化中...' : '一键优化'}</span>
                          </Button>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="headline">主标题</Label>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => generateWithAI('headline')}
                            disabled={isGenerating || !formData.business}
                            className="flex items-center space-x-1"
                          >
                            <Sparkles className="h-4 w-4" />
                            <span>{isGenerating ? '生成中...' : 'AI生成'}</span>
                          </Button>
                        </div>
                        <Input
                          id="headline"
                          value={formData.headline}
                          onChange={(e) => setFormData(prev => ({ ...prev, headline: e.target.value }))}
                          placeholder="吸引人的主标题"
                        />
                      </div>
                      <div>
                        <Label htmlFor="subheadline">副标题</Label>
                        <Input
                          id="subheadline"
                          value={formData.subheadline}
                          onChange={(e) => setFormData(prev => ({ ...prev, subheadline: e.target.value }))}
                          placeholder="详细说明产品价值"
                        />
                      </div>
                      <div>
                        <div className="flex items-center justify-between">
                          <Label htmlFor="cta">行动按钮文字</Label>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => generateWithAI('cta')}
                            disabled={isGenerating || !formData.business}
                            className="flex items-center space-x-1"
                          >
                            <Sparkles className="h-4 w-4" />
                            <span>{isGenerating ? '生成中...' : 'AI生成'}</span>
                          </Button>
                        </div>
                        <Input
                          id="cta"
                          value={formData.cta}
                          onChange={(e) => setFormData(prev => ({ ...prev, cta: e.target.value }))}
                          placeholder="例如：立即免费试用、获取报价、开始使用"
                        />
                      </div>
                      <div>
                        <div className="flex items-center justify-between">
                          <Label>核心功能/特色 (最多4个)</Label>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => generateWithAI('features')}
                            disabled={isGenerating || !formData.business}
                            className="flex items-center space-x-1"
                          >
                            <Sparkles className="h-4 w-4" />
                            <span>{isGenerating ? '生成中...' : 'AI生成'}</span>
                          </Button>
                        </div>
                        {formData.features.map((feature, index) => (
                          <Input
                            key={index}
                            value={feature}
                            onChange={(e) => {
                              const newFeatures = [...formData.features]
                              newFeatures[index] = e.target.value
                              setFormData(prev => ({ ...prev, features: newFeatures }))
                            }}
                            placeholder={`功能特色 ${index + 1}`}
                            className="mt-2"
                          />
                        ))}
                        {formData.features.length < 4 && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFormData(prev => ({ ...prev, features: [...prev.features, ''] }))}
                            className="mt-2"
                          >
                            添加功能
                          </Button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 步骤4: 视觉设计 */}
                  {currentStep === 4 && (
                    <div className="space-y-6">
                      <div>
                        <Label>配色方案</Label>
                        <div className="grid grid-cols-3 gap-3 mt-2">
                          {['blue', 'green', 'purple', 'red', 'orange', 'teal'].map((color) => (
                            <button
                              key={color}
                              onClick={() => setFormData(prev => ({ ...prev, colorScheme: color }))}
                              className={`p-4 rounded-lg border-2 transition-all ${
                                formData.colorScheme === color ? 'border-blue-500' : 'border-gray-200'
                              }`}
                            >
                              <div className={`w-full h-8 rounded bg-${color}-500 mb-2`}></div>
                              <span className="text-sm capitalize">{color}</span>
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <Label>布局风格</Label>
                        <div className="grid grid-cols-2 gap-3 mt-2">
                          {[
                            { key: 'modern', name: '现代简约', desc: '简洁大方的设计' },
                            { key: 'classic', name: '经典商务', desc: '传统专业的风格' },
                            { key: 'creative', name: '创意活泼', desc: '富有创意的设计' },
                            { key: 'minimal', name: '极简主义', desc: '极简的设计理念' }
                          ].map((layout) => (
                            <button
                              key={layout.key}
                              onClick={() => setFormData(prev => ({ ...prev, layout: layout.key }))}
                              className={`p-4 rounded-lg border-2 transition-all text-left ${
                                formData.layout === layout.key ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                              }`}
                            >
                              <div className="font-medium">{layout.name}</div>
                              <div className="text-sm text-gray-500">{layout.desc}</div>
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 步骤5: 页面预览 */}
                  {currentStep === 5 && (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-4">
                        <span className="text-sm font-medium">预览设备：</span>
                        <div className="flex space-x-2">
                          {[
                            { key: 'desktop', icon: Monitor, label: '桌面' },
                            { key: 'tablet', icon: Tablet, label: '平板' },
                            { key: 'mobile', icon: Smartphone, label: '手机' }
                          ].map(({ key, icon: Icon, label }) => (
                            <button
                              key={key}
                              onClick={() => setPreviewMode(key as any)}
                              className={`flex items-center space-x-1 px-3 py-1 rounded text-sm ${
                                previewMode === key ? 'bg-blue-100 text-blue-600' : 'text-gray-600'
                              }`}
                            >
                              <Icon className="h-4 w-4" />
                              <span>{label}</span>
                            </button>
                          ))}
                        </div>
                      </div>
                      
                      <div className="bg-gray-100 p-4 rounded-lg">
                        <div className="bg-white rounded shadow-lg overflow-hidden" 
                             style={{ 
                               width: previewMode === 'desktop' ? '100%' : 
                                      previewMode === 'tablet' ? '768px' : '375px',
                               maxWidth: '100%',
                               margin: '0 auto'
                             }}>
                          {generatedHtml && (
                            <iframe
                              srcDoc={generatedHtml}
                              className="w-full"
                              style={{ height: '500px' }}
                              title="Landing Page Preview"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 步骤6: 代码交付 */}
                  {currentStep === 6 && (
                    <div className="space-y-6">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <CheckCircle className="h-8 w-8 text-green-600" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-2">恭喜！你的Landing Page已经完成</h3>
                        <p className="text-gray-600">现在你可以下载完整的HTML代码，直接部署到你的服务器</p>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">包含内容：</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• 完整的HTML结构</li>
                          <li>• 内联CSS样式</li>
                          <li>• 响应式设计(适配手机、平板、桌面)</li>
                          <li>• 联系表单和交互功能</li>
                          <li>• SEO优化的基础设置</li>
                          <li>• 平滑滚动和动画效果</li>
                        </ul>
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-medium text-blue-900 mb-2">部署指南：</h4>
                        <ol className="text-sm text-blue-800 space-y-1">
                          <li>1. 下载HTML文件到本地</li>
                          <li>2. 将文件上传到你的网站根目录</li>
                          <li>3. 修改联系信息(邮箱、电话等)</li>
                          <li>4. 根据需要调整内容和样式</li>
                          <li>5. 设置域名解析，让页面上线</li>
                        </ol>
                      </div>
                      
                      <div className="flex space-x-3">
                        <Button 
                          onClick={downloadHtml} 
                          className="flex-1 flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700"
                        >
                          <Download className="h-4 w-4" />
                          <span>下载HTML文件</span>
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => setCurrentStep(5)}
                          className="flex items-center space-x-2"
                        >
                          <Eye className="h-4 w-4" />
                          <span>重新预览</span>
                        </Button>
                      </div>

                      <div className="border-t pt-4">
                        <h4 className="font-medium mb-3">分享你的成果</h4>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              navigator.clipboard.writeText(window.location.href)
                              alert('链接已复制到剪贴板！')
                            }}
                            className="flex items-center space-x-1"
                          >
                            <FileText className="h-4 w-4" />
                            <span>复制链接</span>
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              const text = `我刚刚用AI制作了一个专业的Landing Page！快来试试这个工具：${window.location.href}`
                              window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`, '_blank')
                            }}
                            className="flex items-center space-x-1"
                          >
                            <span>分享到Twitter</span>
                          </Button>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 text-yellow-800 mb-2">
                          <Rocket className="h-5 w-5" />
                          <span className="font-medium">进阶建议</span>
                        </div>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          <li>• 添加Google Analytics追踪用户行为</li>
                          <li>• 集成在线客服系统提升转化率</li>
                          <li>• 设置A/B测试优化页面效果</li>
                          <li>• 连接CRM系统管理潜在客户</li>
                          <li>• 定期更新内容保持页面活力</li>
                        </ul>
                      </div>
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>

              {/* 导航按钮 */}
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrev}
                  disabled={currentStep === 1}
                  className="flex items-center space-x-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>上一步</span>
                </Button>
                
                <Button
                  onClick={handleNext}
                  disabled={false}
                  className="flex items-center space-x-2"
                >
                  <span>{currentStep === builderSteps.length ? '完成' : '下一步'}</span>
                  {currentStep < builderSteps.length && <ArrowRight className="h-4 w-4" />}
                </Button>
              </div>
            </Card>
          </div>

          {/* 右侧：预览区域 */}
          <div>
            <Card className="p-6 h-full">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">实时预览</h3>
                <Badge variant="secondary">
                  {completedSteps.length}/{builderSteps.length} 完成
                </Badge>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 h-[600px] overflow-y-auto">
                {currentStep < 5 ? (
                  <div className="h-full flex items-center justify-center text-gray-400">
                    <div className="text-center">
                      <Layout className="h-12 w-12 mx-auto mb-2" />
                      <p>完成左侧表单后可预览效果</p>
                    </div>
                  </div>
                ) : generatedHtml ? (
                  <iframe
                    srcDoc={generatedHtml}
                    className="w-full h-full border-0 rounded"
                    title="Landing Page Preview"
                  />
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                )}
              </div>
            </Card>
          </div>
        </div>

        {/* 底部提示 */}
        <div className="mt-8 text-center">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-center space-x-2 text-blue-700">
              <Sparkles className="h-5 w-5" />
              <span className="font-medium">AI驱动的智能建站</span>
            </div>
            <p className="text-blue-600 text-sm mt-1">
              基于你的输入，AI将自动优化内容和设计，让你的Landing Page更具转化力
            </p>
          </div>
          
          {/* 统计信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-white rounded-lg p-4 shadow-sm border">
              <div className="text-2xl font-bold text-blue-600">
                {isClient ? formatNumber(stats.totalGenerated) : '1.2k'}
              </div>
              <div className="text-sm text-gray-600">总制作数</div>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm border">
              <div className="text-2xl font-bold text-green-600">
                {isClient ? formatNumber(stats.totalDownloaded) : '892'}
              </div>
              <div className="text-sm text-gray-600">总下载数</div>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm border">
              <div className="text-2xl font-bold text-orange-600">
                {isClient ? stats.todayGenerated : '23'}
              </div>
              <div className="text-sm text-gray-600">今日制作</div>
            </div>
            <div className="bg-white rounded-lg p-4 shadow-sm border">
              <div className="text-2xl font-bold text-purple-600">
                {isClient ? formatNumber(stats.activeUsers) : '156'}
              </div>
              <div className="text-sm text-gray-600">活跃用户</div>
            </div>
          </div>
        </div>

        {/* SEO优化内容区域 */}
        <div className="mt-16 space-y-12">
          {/* 关于AI落地页生成器 */}
          <section className="bg-white rounded-lg p-8 shadow-sm border">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">AI落地页生成器 - 5分钟制作专业落地页</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">什么是AI落地页生成器？</h3>
                <p className="text-gray-600 mb-4">
                  AIQuickSite的AI落地页生成器是一款基于人工智能技术的专业建站工具。它能够根据用户的业务需求和目标受众，
                  自动生成高质量的落地页(Landing Page)。无需编程基础，5分钟内即可完成一个专业的营销页面制作。
                </p>
                <p className="text-gray-600 mb-4">
                  我们的AI系统经过大量的优秀落地页案例训练，能够智能分析您的业务特点，自动生成符合行业标准的页面布局、
                  文案内容和视觉设计，确保每个生成的落地页都具备良好的用户体验和转化效果。
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">核心功能特色</h3>
                <ul className="text-gray-600 space-y-2">
                  <li>• <strong>AI智能内容生成</strong>：自动生成吸引人的标题、副标题和产品介绍</li>
                  <li>• <strong>响应式设计</strong>：自适应桌面、平板和手机等所有设备</li>
                  <li>• <strong>SEO友好</strong>：内置搜索引擎优化最佳实践</li>
                  <li>• <strong>快速部署</strong>：一键生成HTML代码，支持直接部署</li>
                  <li>• <strong>专业模板</strong>：多种行业模板，覆盖各类业务场景</li>
                  <li>• <strong>完全免费</strong>：基础功能完全免费使用，无隐藏费用</li>
                </ul>
              </div>
            </div>
          </section>

          {/* 适用场景 */}
          <section className="bg-gray-50 rounded-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">落地页适用场景</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">产品推广</h3>
                <p className="text-gray-600 mb-4">
                  为新产品发布、软件应用推广、课程销售等创建专业的产品推广页面。
                </p>
                <p className="text-sm text-gray-500">
                  适合：APP下载页、课程销售页、产品介绍页、软件推广页
                </p>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">活动营销</h3>
                <p className="text-gray-600 mb-4">
                  为会议、研讨会、促销活动、线上活动等创建高转化的活动宣传页面。
                </p>
                <p className="text-sm text-gray-500">
                  适合：会议报名页、活动宣传页、促销活动页、在线研讨会
                </p>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-3">服务展示</h3>
                <p className="text-gray-600 mb-4">
                  为咨询服务、设计服务、培训服务等专业服务创建客户获取页面。
                </p>
                <p className="text-sm text-gray-500">
                  适合：咨询服务页、设计作品展示、培训课程介绍、专业服务推广
                </p>
              </div>
            </div>
          </section>

          {/* 制作流程 */}
          <section className="bg-white rounded-lg p-8 shadow-sm border">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">5分钟制作流程</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">1</span>
                </div>
                <h3 className="text-lg font-semibold mb-2">需求输入</h3>
                <p className="text-gray-600">描述您的业务、目标受众和营销目标</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">2</span>
                </div>
                <h3 className="text-lg font-semibold mb-2">AI分析</h3>
                <p className="text-gray-600">AI智能分析并生成最适合的内容和设计</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">3</span>
                </div>
                <h3 className="text-lg font-semibold mb-2">一键生成</h3>
                <p className="text-gray-600">获取完整HTML代码，直接部署使用</p>
              </div>
            </div>
          </section>

          {/* 常见问题 */}
          <section className="bg-gray-50 rounded-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">常见问题解答</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">落地页和普通网站有什么区别？</h3>
                <p className="text-gray-600">
                  落地页(Landing Page)是专门为营销目的设计的单页面网站，专注于一个特定的转化目标，
                  如产品销售、用户注册、资料下载等。相比普通网站，落地页更加专注和高效，
                  能够显著提高访客的转化率。
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">生成的落地页可以SEO优化吗？</h3>
                <p className="text-gray-600">
                  是的！我们生成的落地页代码完全符合SEO最佳实践，包括合理的HTML结构、
                  meta标签设置、语义化标签使用等。您可以进一步自定义标题、描述、
                  关键词等SEO元素来提高搜索引擎排名。
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">落地页适合哪些行业？</h3>
                <p className="text-gray-600">
                  几乎所有需要在线营销的行业都适用，包括但不限于：电商零售、教育培训、
                  软件服务、咨询服务、医疗健康、房地产、金融服务、餐饮娱乐等。
                  我们的AI系统针对不同行业进行了优化训练。
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">生成的代码可以商用吗？</h3>
                <p className="text-gray-600">
                  完全可以！通过我们平台生成的落地页代码，您拥有完全的使用权和修改权，
                  可以用于任何商业用途，包括为客户提供服务、销售产品等，无需支付额外费用。
                </p>
              </div>
            </div>
          </section>

          {/* 相关资源 */}
          <section className="bg-white rounded-lg p-8 shadow-sm border">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">相关资源与学习</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">落地页设计最佳实践</h3>
                <ul className="text-gray-600 space-y-2">
                  <li>• 明确的价值主张和吸引人的标题</li>
                  <li>• 简洁清晰的页面布局和导航</li>
                  <li>• 强有力的行动号召(CTA)按钮</li>
                  <li>• 社会证明和客户评价展示</li>
                  <li>• 移动端优化和快速加载速度</li>
                  <li>• A/B测试持续优化转化效果</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">扩展学习资源</h3>
                <ul className="text-gray-600 space-y-2">
                  <li>• <strong>网站建设课程</strong>：从落地页到完整网站</li>
                  <li>• <strong>SEO优化指南</strong>：提高搜索引擎排名</li>
                  <li>• <strong>转化率优化</strong>：提升页面转化效果</li>
                  <li>• <strong>数字营销策略</strong>：全方位营销推广</li>
                  <li>• <strong>用户体验设计</strong>：提升用户满意度</li>
                  <li>• <strong>数据分析工具</strong>：跟踪和分析效果</li>
                </ul>
              </div>
            </div>
          </section>

          {/* 关键词和标签 */}
          <section className="bg-blue-50 rounded-lg p-8">
            <div className="text-center">
              <h2 className="text-xl font-bold text-gray-900 mb-4">立即开始制作您的专业落地页</h2>
              <p className="text-gray-600 mb-6">
                免费AI落地页生成器，5分钟制作专业营销页面。支持产品推广、活动营销、服务展示等多种场景。
                响应式设计，SEO友好，完全免费使用。
              </p>
              <div className="flex flex-wrap justify-center gap-2 text-sm">
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">AI落地页生成器</span>
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">免费建站工具</span>
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">营销页面制作</span>
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">响应式设计</span>
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">SEO优化</span>
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">产品推广页</span>
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">活动营销页</span>
                <span className="bg-white px-3 py-1 rounded-full text-gray-700">专业建站</span>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  )
} 