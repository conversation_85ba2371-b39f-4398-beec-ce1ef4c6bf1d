import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '免费AI落地页生成器 - 5分钟制作专业落地页 | AIQuickSite',
  description: 'AIQuickSite免费AI落地页生成器，5分钟制作专业营销页面。支持产品推广、活动营销、服务展示。响应式设计，SEO友好，完全免费使用。',
  keywords: [
    'AI落地页生成器',
    '免费建站工具',
    '营销页面制作',
    '落地页设计',
    '产品推广页',
    '活动营销页',
    '响应式设计',
    'SEO优化',
    'AIQuickSite',
    '5分钟建站',
    '专业落地页',
    '免费网站制作'
  ],
  openGraph: {
    title: '免费AI落地页生成器 - 5分钟制作专业落地页 | AIQuickSite',
    description: 'AIQuickSite免费AI落地页生成器，5分钟制作专业营销页面。支持产品推广、活动营销、服务展示。',
    type: 'website',
    url: 'https://aiquicksite.com/interactive-course',
    siteName: 'AIQuickSite',
  },
  twitter: {
    card: 'summary_large_image',
    title: '免费AI落地页生成器 - 5分钟制作专业落地页',
    description: 'AIQuickSite免费AI落地页生成器，5分钟制作专业营销页面。响应式设计，SEO友好，完全免费使用。',
  },
  alternates: {
    canonical: 'https://aiquicksite.com/interactive-course',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  }
}

export default function LandingPageBuilderLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
} 