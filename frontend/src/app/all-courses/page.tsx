'use client'

import { useEffect, useState } from 'react'
import { Course, CourseService } from '@/lib/services/course.service'
import { CourseCard } from '@/components/course/course-card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function CoursesPage() {
  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const data = await CourseService.getCourses()
        setCourses(data)
      } catch (err) {
        setError('请先登录后再查看课程')
        console.error('Failed to fetch courses:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchCourses()
  }, [])

  // 按难度级别对课程进行分组
  const coursesByLevel = {
    '综合基础': courses.filter(course => course.level === '综合基础'),
    '专项进阶': courses.filter(course => course.level === '专项进阶'),
    '技术高阶': courses.filter(course => course.level === '技术高阶'),
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error}
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* 综合基础课程 */}
      <section className="mb-12">
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-2xl font-semibold">综合基础</h2>
          <div className="flex-1 h-[1px] bg-border"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {coursesByLevel['综合基础'].map((course) => (
            <CourseCard
              key={course.id}
              id={course.id}
              title={course.title}
              description={course.description}
              duration={course.duration}
              level={course.level}
              lessonsCount={course.lessonsCount}
              price={course.price}
              isPurchased={course.isPurchased}
            />
          ))}
        </div>
      </section>

      {/* 专项进阶课程 */}
      <section className="mb-12">
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-2xl font-semibold">专项进阶</h2>
          <div className="flex-1 h-[1px] bg-border"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {coursesByLevel['专项进阶'].map((course) => (
            <CourseCard
              key={course.id}
              id={course.id}
              title={course.title}
              description={course.description}
              duration={course.duration}
              level={course.level}
              lessonsCount={course.lessonsCount}
              price={course.price}
              isPurchased={course.isPurchased}
            />
          ))}
        </div>
      </section>

      {/* 技术高阶课程 */}
      <section className="mb-12">
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-2xl font-semibold">技术高阶</h2>
          <div className="flex-1 h-[1px] bg-border"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {coursesByLevel['技术高阶'].map((course) => (
            <CourseCard
              key={course.id}
              id={course.id}
              title={course.title}
              description={course.description}
              duration={course.duration}
              level={course.level}
              lessonsCount={course.lessonsCount}
              price={course.price}
              isPurchased={course.isPurchased}
            />
          ))}
        </div>
      </section>
    </div>
  )
} 