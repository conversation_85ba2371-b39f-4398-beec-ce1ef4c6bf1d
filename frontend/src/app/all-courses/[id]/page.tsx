'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Course, CourseLesson, CourseService } from '@/lib/services/course.service';
import { Clock, BookOpen } from 'lucide-react';
import Link from 'next/link';

export default function CoursePage() {
  const params = useParams();
  const courseId = params.id as string;

  const [course, setCourse] = useState<Course | null>(null);
  const [lessons, setLessons] = useState<CourseLesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourseData = async () => {
      try {
        // 获取课程信息和章节列表
        const [courseData, lessonsData] = await Promise.all([
          CourseService.getCourse(courseId),
          CourseService.getCoursePublicLessons(courseId).catch(() => []),
        ]);

        console.log('课程数据:', courseData);
        console.log('课程价格:', courseData.price, typeof courseData.price);
        
        setCourse(courseData);
        setLessons(lessonsData);
      } catch (err) {
        setError('加载课程信息失败，请稍后重试');
        console.error('Failed to fetch course:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourseData();
  }, [courseId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="text-center text-red-500 min-h-[400px] flex items-center justify-center">
        {error || '课程不存在'}
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 课程头部信息 */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Badge variant="secondary">{course.level}</Badge>
            <Badge variant="default" className={`${
              Number(course.price) === 0 
                ? "bg-green-500" 
                : "bg-blue-500"
            }`}>
              {Number(course.price) === 0 ? "免费" : `$${course.price}`}
            </Badge>
          </div>
          <h1 className="text-3xl font-bold mb-4">{course.title}</h1>
          <p className="text-lg text-muted-foreground mb-6">{course.description}</p>
          <div className="flex items-center gap-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span>{course.duration}</span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <span>{course.lessonsCount} 课时</span>
            </div>
          </div>
        </div>

        {/* 购买按钮 */}
        <div className="mb-8">
          <Button 
            asChild 
            size="lg" 
            className={`w-full md:w-auto ${
              Number(course.price) === 0 
                ? "bg-green-600 hover:bg-green-700" 
                : ""
            }`}
          >
            <Link href={
              Number(course.price) === 0 
                ? `/all-courses/free-enroll/${course.id}` 
                : `/all-courses/purchase/${course.id}`
            }>
              {Number(course.price) === 0 ? "立即加入" : "开始学习"}
            </Link>
          </Button>
        </div>

        {/* 课程章节列表 */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold mb-4">课程大纲</h2>
          {lessons.map((lesson) => (
            <Card key={lesson.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground">第 {lesson.order} 章</span>
                  </div>
                  <h3 className="text-lg font-medium mt-1">{lesson.title}</h3>
                  <p className="text-muted-foreground mt-1">{lesson.description}</p>
                  <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{lesson.duration}</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
} 