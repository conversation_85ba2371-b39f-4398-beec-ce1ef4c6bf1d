'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { <PERSON>R<PERSON>, BookO<PERSON>, Palette, Sparkles, Star } from 'lucide-react'
import { useAuth } from '@/auth/contexts/simplified-auth-context'

const trustPoints = [
  {
    icon: BookOpen,
    title: '1000+ 精选绘本'
  },
  {
    icon: Palette,
    title: '多种绘画风格'
  },
  {
    icon: Sparkles,
    title: 'AI智能创作'
  },
  {
    icon: Star,
    title: '5星好评率'
  }
]

export function CTASection() {
  const { isAuthenticated, isInitialized } = useAuth()

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-pink-400 via-purple-400 to-indigo-400">
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
      <div className="container relative py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center space-y-6 max-w-3xl mx-auto"
        >
          <h2 className="text-3xl font-bold text-white">
            {isAuthenticated
              ? '开始创作属于你的绘本故事'
              : '准备好创作你的第一本绘本了吗？'}
          </h2>
          <p className="text-lg text-white/90">
            {isAuthenticated
              ? '使用AI智能创作工具，让绘本创作变得简单有趣'
              : '立即注册，免费体验AI智能绘本创作平台'}
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
            {isInitialized && (
              isAuthenticated ? (
                <>
                  <Button
                    size="lg"
                    variant="secondary"
                    className="w-full sm:w-auto bg-white/10 text-white hover:bg-white/20"
                    asChild
                  >
                    <Link href="/create">
                      开始创作
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button
                    size="lg"
                    variant="secondary"
                    className="w-full sm:w-auto bg-white/90 text-slate-800 hover:bg-white transition-all duration-300"
                    asChild
                  >
                    <Link href="/library">
                      浏览绘本库
                    </Link>
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    size="lg"
                    variant="secondary"
                    className="w-full sm:w-auto bg-white/10 text-white hover:bg-white/20"
                    asChild
                  >
                    <Link href="/register">
                      免费注册
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button
                    size="lg"
                    variant="secondary"
                    className="w-full sm:w-auto bg-white/90 text-slate-800 hover:bg-white transition-all duration-300"
                    asChild
                  >
                    <Link href="/login">
                      登录账户
                    </Link>
                  </Button>
                </>
              )
            )}
          </div>
        </motion.div>

        {/* Trust Points */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          {trustPoints.map((point, index) => {
            const Icon = point.icon
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="flex flex-col items-center text-center"
              >
                <div className="bg-white/10 p-3 rounded-lg mb-4">
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-sm text-white">
                  {point.title}
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>
    </div>
  )
} 