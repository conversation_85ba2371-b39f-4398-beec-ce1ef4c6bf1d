'use client'

import { motion } from 'framer-motion'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const faqs = [
  {
    question: "为什么要从落地页开始学建站？",
    answer: "落地页是网站的基础，结构相对简单但包含了设计的核心要素。从落地页开始能让你快速上手，建立信心，然后逐步学习更复杂的网站构建技能。"
  },
  {
    question: "我完全没有基础，能学会吗？",
    answer: "当然可以！我们的课程专为零基础学习者设计。通过AI工具辅助，你可以快速制作出专业的落地页，同时学习设计和开发的基本概念。"
  },
  {
    question: "工具和课程是怎么结合的？",
    answer: "你可以先使用AI工具制作落地页，在实践过程中学习基本概念，然后通过系统课程深入理解设计原理和技术知识，最后应用到更复杂的网站项目中。"
  },
  {
    question: "学完能做什么样的网站？",
    answer: "从简单的落地页开始，逐步学会制作企业官网、个人博客、电商网站等各类网站。课程涵盖设计、开发、SEO、部署等完整建站流程。"
  },
  {
    question: "需要多长时间能学会？",
    answer: "制作第一个落地页只需要30分钟，完整的建站技能培养需要2-3个月。你可以根据自己的时间安排灵活学习，每天投入1-2小时即可。"
  },
  {
    question: "有学习支持吗？",
    answer: "我们提供完整的学习支持，包括在线答疑、学习社群、定期直播课程，以及一对一的学习指导。确保每个学员都能顺利完成学习目标。"
  }
]

export function FAQ() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">常见问题</h2>
          <p className="mt-4 text-xl text-gray-600">解答关于学习建站的疑惑</p>
        </div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mt-12"
        >
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent>
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </motion.div>
      </div>
    </section>
  )
} 