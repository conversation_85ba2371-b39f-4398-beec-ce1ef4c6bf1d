'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>, ArrowRight, Clock } from 'lucide-react'
import { socialProof } from '@/data/home/<USER>'
import Image from 'next/image'

const fadeIn = {
  hidden: { 
    opacity: 0, 
    y: 20,
    transition: { duration: 0.5 }
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.5 }
  }
}

export function HeroSection() {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-blue-50">
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative z-10 py-16 sm:py-24 md:py-32">
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex items-center justify-center mb-6"
            >
              <div className="flex items-center bg-green-100 text-green-800 px-4 py-2 rounded-full">
                <Clock className="h-4 w-4 mr-2" />
                <span className="text-sm font-semibold">5分钟制作专业落地页</span>
              </div>
            </motion.div>
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"
            >
              <span className="block">免费制作你的专业落地页</span>
              <span className="block bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600">一个页面就是一个网站</span>
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-xl text-gray-600 max-w-2xl mx-auto"
            >
              5分钟制作专业落地页，满足你的展示需求。一个精美的落地页就是一个完整的网站，需要更多功能时再学习扩展。
            </motion.p>
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mt-8 flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <Button size="lg" className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white" asChild>
                <Link 
                  href="/interactive-course" 
                  className="px-8 py-3 flex items-center justify-center"
                >
                  免费制作落地页
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="w-full sm:w-auto" asChild>
                <Link 
                  href="#examples" 
                  className="px-8 py-3 flex items-center justify-center"
                >
                  查看示例
                </Link>
              </Button>
            </motion.div>
          
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="mt-12 flex flex-wrap justify-center gap-x-8 gap-y-4"
            >
              {socialProof.map((proof, index) => (
                <div key={index} className="flex items-center text-gray-600">
                  <Star className="h-5 w-5 text-yellow-400 mr-2" />
                  <span>{proof}</span>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 