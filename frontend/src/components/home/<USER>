'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  Clock, 
  CheckCircle, 
  ArrowRight,
  Star,
  Users,
  Gift
} from 'lucide-react'

const advantages = [
  {
    icon: Clock,
    title: '5分钟完成',
    description: '快速制作专业落地页，立即满足展示需求'
  },
  {
    icon: CheckCircle,
    title: '即时可用',
    description: '制作完成即可发布，无需等待审核'
  },
  {
    icon: Zap,
    title: '完全免费',
    description: '基础功能完全免费，满足大部分需求'
  }
]

export function FreeCourseHighlight() {
  return (
    <section className="py-16 bg-gradient-to-r from-green-50 via-blue-50 to-purple-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <Badge className="mb-4 bg-green-100 text-green-800 border-green-200">
            <Gift className="w-4 h-4 mr-1" />
            完全免费体验
          </Badge>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            现在就开始制作你的落地页
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            5分钟制作专业落地页，立即满足你的展示需求。一个页面就是一个完整的网站！
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* 左侧：优势特点 */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                为什么选择我们的落地页工具？
              </h3>
              <div className="space-y-6">
                {advantages.map((advantage, index) => {
                  const Icon = advantage.icon
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start space-x-4"
                    >
                      <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Icon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 mb-2">
                          {advantage.title}
                        </h4>
                        <p className="text-gray-600">
                          {advantage.description}
                        </p>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>

            {/* 社会证明 */}
            <div className="flex items-center space-x-6 pt-4 border-t">
              <div className="flex items-center space-x-2">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <span className="text-sm text-gray-600">4.9/5 用户评分</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-gray-400" />
                <span className="text-sm text-gray-600">8000+ 落地页已创建</span>
              </div>
            </div>
          </motion.div>

          {/* 右侧：制作体验卡片 */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
          >
            <Card className="p-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  5分钟制作落地页
                </h3>
                <p className="text-gray-600 mb-4">
                  选择模板，添加内容，立即发布
                </p>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  完全免费 • 立即可用
                </Badge>
              </div>

              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>AI智能设计，一键生成</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>可视化编辑，实时预览</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>响应式设计，适配所有设备</span>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                  <span>免费域名，一键发布</span>
                </div>
              </div>

              <Button 
                asChild 
                size="lg"
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              >
                <Link href="/interactive-course" className="flex items-center justify-center">
                  现在就开始制作
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>

              <p className="text-xs text-gray-500 text-center mt-3">
                需要更多功能时，可随时升级学习完整建站课程
              </p>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
} 