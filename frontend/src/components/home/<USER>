'use client'

import Image from 'next/image'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Check, Zap, BookOpen, ArrowUp } from 'lucide-react'

const features = [
  {
    name: '5分钟制作落地页',
    description: '使用AI工具快速制作专业落地页，无需任何技术基础，拖拽式操作，实时预览效果。',
    icon: Zap,
    benefits: [
      'AI智能设计',
      '可视化编辑',
      '实时预览'
    ]
  },
  {
    name: '一个页面就够用',
    description: '大多数展示需求一个精美的落地页就能满足。个人展示、产品推广、活动宣传都不在话下。',
    icon: Check,
    benefits: [
      '个人作品展示',
      '产品服务推广',
      '活动信息发布'
    ]
  },
  {
    name: '需要时再扩展',
    description: '当你需要更多功能时，我们提供系统化课程帮你扩展。从单页到多页，从展示到交互。',
    icon: ArrowUp,
    benefits: [
      '多页面网站',
      '高级功能集成',
      '系统化课程'
    ]
  }
]

export function FeaturesSection() {
  return (
    <section id="features" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">从简单开始，按需扩展</h2>
          <p className="mt-4 text-xl text-gray-600">先满足当下需求，再根据实际情况学习更多</p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-12 lg:grid-cols-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="relative"
              >
                <div>
                  <div className="absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                    {feature.icon && <feature.icon className="h-6 w-6" />}
                  </div>
                  <div className="ml-16">
                    <h3 className="text-xl font-medium text-gray-900">{feature.name}</h3>
                    <p className="mt-2 text-base text-gray-600">{feature.description}</p>
                    <ul className="mt-4 space-y-3">
                      {feature.benefits.map((benefit) => (
                        <li key={benefit} className="flex items-center text-gray-600">
                          <svg className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          {benefit}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
} 