'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { User, Store, Briefcase, ArrowRight } from 'lucide-react'

const scenarios = [
  {
    title: '个人展示',
    icon: User,
    description: '展示个人技能、作品或服务的专业页面',
    features: [
      '个人简介展示',
      '作品集展示',
      '联系方式获取'
    ],
    example: '自由设计师、摄影师、咨询师',
    image: '/images/scenarios/personal.png'
  },
  {
    title: '产品推广',
    icon: Store,
    description: '单一产品或服务的专业推广页面',
    features: [
      '产品特色介绍',
      '用户评价展示',
      '购买转化引导'
    ],
    example: '新产品发布、课程推广、应用下载',
    image: '/images/scenarios/product.png'
  },
  {
    title: '活动宣传',
    icon: Briefcase,
    description: '活动、会议或服务的专业宣传页面',
    features: [
      '活动详情介绍',
      '报名表单收集',
      '社交媒体分享'
    ],
    example: '会议活动、培训课程、营销活动',
    image: '/images/scenarios/event.png'
  }
]

export function ScenarioSection() {
  return (
    <section id="examples" className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">一个落地页，多种用途</h2>
          <p className="mt-4 text-xl text-gray-600">满足你的各种展示和推广需求</p>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-3">
          {scenarios.map((scenario, index) => (
            <motion.div
              key={scenario.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center p-3 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl mb-4">
                  {scenario.icon && <scenario.icon className="h-6 w-6 text-blue-600" />}
                </div>
                <h3 className="text-2xl font-semibold text-gray-900">{scenario.title}</h3>
                <p className="mt-2 text-gray-600">{scenario.description}</p>
              </div>
              
              <ul className="space-y-3 mb-6">
                {scenario.features.map((feature) => (
                  <li key={feature} className="flex items-center text-gray-600">
                    <svg className="h-5 w-5 text-blue-500 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>

              <div className="border-t pt-4">
                <p className="text-sm text-gray-500">
                  <span className="font-medium">适用场景：</span>{scenario.example}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-12 text-center"
        >
          <div className="bg-blue-50 rounded-xl p-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              想要更多功能？随时可以扩展！
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              一个落地页就能满足大部分需求。当你需要添加博客、电商功能、用户系统等更复杂功能时，
              我们提供系统化的课程帮你逐步扩展网站功能。
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  )
} 