'use client'

import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Loader2 } from 'lucide-react'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'

// 登录表单验证模式
const loginSchema = z.object({
  email: z.string()
    .email('请输入有效的邮箱地址'),
  password: z.string()
    .min(1, '请输入密码'),
})

type FormData = z.infer<typeof loginSchema>

interface LoginFormProps {
  onSubmit: (data: FormData) => Promise<void>
}

export function LoginForm({ onSubmit }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const form = useForm<FormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const handleSubmit = async (data: FormData) => {
    try {
      console.log('[LoginForm] 表单提交开始', {
        data,
        timestamp: new Date().toISOString()
      })
      setIsLoading(true)
      await onSubmit(data)
      console.log('[LoginForm] 表单提交成功', {
        timestamp: new Date().toISOString()
      })
    } catch (error: any) {
      // 根据错误类型显示不同的错误信息
      let errorMessage = '登录失败，请稍后重试'

      console.error('[LoginForm] 表单提交失败', {
        error,
        errorCode: error?.code,
        errorMessage: error?.message,
        timestamp: new Date().toISOString()
      })

      if (error?.code === 'INVALID_CREDENTIALS') {
        errorMessage = error.message || '邮箱或密码错误'
      } else if (error?.code === 'NETWORK_ERROR') {
        errorMessage = '网络连接失败，请检查网络后重试'
      }

      toast({
        title: '登录失败',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn(
      "transition-opacity duration-200",
      isClient ? "opacity-100" : "opacity-0"
    )}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>邮箱</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="请输入邮箱地址"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>密码</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="请输入密码"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            登录
          </Button>
        </form>
      </Form>
    </div>
  )
}