import { User } from '@/lib/services/user.service'
import { authApi } from './auth-api'
import { tokenManager } from '../core/token-manager'

class AuthService {
  async getCurrentUser(): Promise<User | null> {
    try {
      const token = tokenManager.getToken()
      if (!token) {
        return null
      }

      const user = await authApi.getCurrentUser()
      return user
    } catch (error) {
      console.error('[AuthService] 获取当前用户失败:', error)
      return null
    }
  }

  async login(credentials: { email: string; password: string }): Promise<User> {
    try {
      const { user, token } = await authApi.login(credentials)
      tokenManager.setToken(token)
      return user
    } catch (error) {
      console.error('[AuthService] 登录失败:', error)
      throw error
    }
  }

  async logout(): Promise<void> {
    try {
      await authApi.logout()
      tokenManager.clearToken()
    } catch (error) {
      console.error('[AuthService] 登出失败:', error)
      throw error
    }
  }

  async register(data: {
    email: string
    password: string
    name: string
  }): Promise<User> {
    try {
      const { user, token } = await authApi.register(data)
      tokenManager.setToken(token)
      return user
    } catch (error) {
      console.error('[AuthService] 注册失败:', error)
      throw error
    }
  }
}

// 创建并导出单例实例
export const authService = new AuthService()

// 同时导出类本身，以便于测试或其他用途
export { AuthService }

 