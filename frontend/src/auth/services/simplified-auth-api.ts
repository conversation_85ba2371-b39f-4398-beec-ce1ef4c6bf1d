import { api } from '@/lib/api/index'
import { User } from '@/lib/services/user.service'

/**
 * 认证响应类型
 */
interface AuthResponse {
  user: User
}

/**
 * 简化的认证API服务
 */
class AuthApi {
  /**
   * 处理API请求并统一错误处理
   */
  private async handleRequest<T>(
    requestFn: () => Promise<T>,
    errorMessage: string
  ): Promise<T> {
    try {
      return await requestFn()
    } catch (error: any) {
      // 处理认证错误
      if (error?.response?.status === 401) {
        const message = error?.response?.data?.message || '认证失败'
        console.error(`认证错误: ${message}`)
        throw new Error(message)
      }
      
      // 处理其他错误
      console.error(`${errorMessage}:`, error)
      throw error
    }
  }
  
  /**
   * 用户登录
   */
  async login(credentials: { email: string; password: string }): Promise<AuthResponse> {
    return this.handleRequest(
      async () => {
        const response = await api.post<AuthResponse>('/api/auth/login', credentials)
        return response.data
      },
      '登录失败'
    )
  }
  
  /**
   * 管理员登录
   */
  async adminLogin(credentials: { email: string; password: string }): Promise<AuthResponse> {
    return this.handleRequest(
      async () => {
        const response = await api.post<AuthResponse>('/api/auth/admin/login', credentials)
        return response.data
      },
      '管理员登录失败'
    )
  }
  
  /**
   * 用户注册
   */
  async register(data: { 
    email: string; 
    password: string; 
    name: string; 
    verificationCode: string 
  }): Promise<AuthResponse> {
    return this.handleRequest(
      async () => {
        const response = await api.post<AuthResponse>('/api/auth/register', data)
        return response.data
      },
      '注册失败'
    )
  }
  
  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User> {
    return this.handleRequest(
      async () => {
        const response = await api.get<User>('/api/auth/me')
        return response.data
      },
      '获取用户信息失败'
    )
  }
  
  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    return this.handleRequest(
      async () => {
        await api.post('/api/auth/logout')
      },
      '登出失败'
    )
  }
  
  /**
   * 刷新Token
   */
  async refreshToken(): Promise<AuthResponse> {
    return this.handleRequest(
      async () => {
        const response = await api.post<AuthResponse>('/api/auth/refresh')
        return response.data
      },
      'Token刷新失败'
    )
  }
}

// 导出单例实例
export const authApi = new AuthApi()
