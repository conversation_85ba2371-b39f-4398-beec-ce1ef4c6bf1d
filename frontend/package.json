{"name": "admin-core-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_ENV=production next build", "start": "NODE_ENV=production next start -p 3000", "lint": "next lint", "clean": "rm -rf .next", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --maxWorkers=2", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/cssinjs": "^1.22.1", "@ant-design/icons": "^5.5.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.65.1", "@tanstack/react-table": "^8.20.5", "@types/marked": "^6.0.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/socket.io-client": "^3.0.0", "@uiw/react-md-editor": "^4.0.5", "antd": "^5.22.3", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "framer-motion": "^10.18.0", "html2canvas": "^1.4.1", "jwt-decode": "^4.0.0", "katex": "^0.16.19", "lucide-react": "^0.292.0", "marked": "^15.0.6", "mermaid": "^11.4.1", "mobx": "6.12.0", "mobx-react-lite": "4.0.5", "next": "13.5.6", "next-themes": "^0.4.4", "qrcode.react": "^4.2.0", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.49.2", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "reactflow": "^11.11.4", "rehype-katex": "^7.0.1", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "swr": "^2.2.5", "tailwind-merge": "^2.6.0", "unified": "^11.0.5", "zod": "^3.22.4"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.11", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "critters": "^0.0.25", "css-loader": "^7.1.2", "cytoscape-fcose": "2.2.0", "eslint": "^8.56.0", "eslint-config-next": "13.5.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "shadcn-ui": "^0.9.4", "style-loader": "^4.0.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.1.1", "typescript": "^5.7.2"}, "main": "index.js", "license": "MIT"}