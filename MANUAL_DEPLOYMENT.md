# 1、手动部署首次部署指南

## 1. 服务器情况

- Ubuntu 20.04 LTS 或更高版本
- 最小配置：2核CPU，4GB内存，40GB存储
- 已配置好的域名（www.666ai.net）

## 2. 基础环境安装


### 2.1 系统更新
```bash
ssh ubuntu@**************
sudo apt update
sudo apt upgrade -y
```

### 2.2 Node.js 安装
```bash
# 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash source ~/.bashrc

# 安装 Node.js 18
nvm install 23.5.0
nvm use 23.5.0
nvm alias default 23.5.0

# 验证安装
node --version
npm --version
```

### 2.3 PNPM 安装
```bash
# 安装 pnpm
npm install -g pnpm

# 验证安装
pnpm --version
```

### 2.4 PM2 安装
```bash
# 安装 pm2
npm install -g pm2

# 验证安装
pm2 --version
```

### 2.5 PostgreSQL 安装
```bash
# 安装 PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# 验证安装
psql --version
```

### 2.6 Redis 安装
```bash
# 安装 Redis
sudo apt install redis-server -y

redis-server --version

sudo add-apt-repository ppa:redislabs/redis -y
sudo apt-get update

sudo apt-get install redis-server -y

redis-server --version # 应显示7.2.4或更高版本

# 配置 Redis 开机启动
sudo systemctl enable redis-server

# 验证安装
redis-cli --version
```

### 2.7 Nginx 安装
```bash
# 安装 Nginx
sudo apt install nginx -y

# 启动 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 验证安装
nginx -v
```

### 2.8 Git 安装
```bash
sudo apt install git -y

# 验证安装
git --version
```

如需使用git拉取代码，必须先安装git。


## 3. 项目部署

### 3.1 创建部署目录
```bash
# 创建项目目录
sudo mkdir -p /var/www/aiquicksite
sudo chown -R $USER:$USER /var/www/aiquicksite
```

### 3.2 上传项目文件

#### 方法一：使用Git拉取代码（推荐）

1. 配置SSH密钥（如未配置）：
   ```bash
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>" #要输入文件路径，直接回车；密钥密码也可以直接回车
   cat ~/.ssh/id_rsa.pub  # 将公钥添加到Git仓库平台
   ```

登录 GitHub
用浏览器登录你的 GitHub 账号。
进入 SSH Key 设置页面
依次点击：右上角头像 → Settings（设置）→ SSH and GPG keys → New SSH key
粘贴公钥并保存
Title 随便填（比如“我的服务器”）
Key 粘贴刚才复制的内容
点击“Add SSH key”保存
完成！可以用 ssh -T ************** 测试是否成功，需要确认指纹直接回车就可以

2. 拉取代码：
   ```bash
   cd /var/www/aiquicksite
   <NAME_EMAIL>:shuicici/aiquicksite.git .
   ```

#### 方法二：使用SCP/FTP上传

1. 在本地打包项目文件：
   ```bash
   tar czvf aiquicksite.tar.gz your-project-folder/
   ```
2. 上传到服务器：
   ```bash
   scp aiquicksite.tar.gz user@your-server:/var/www/aiquicksite/
   ```
3. 服务器解压：
   ```bash
   tar xzvf aiquicksite.tar.gz
   ```

如遇问题请参考第9节“故障排查”。

## 4 环境文件环境变量配置

### 4.1 后端环境变量 (`/var/www/aiquicksite/backend/.env.production`):
sudo nano /var/www/aiquicksite/backend/.env.production


> ⚠️ **安全提醒：所有密码、密钥等敏感信息请务必更换为强密码，不要使用示例中的密码！**

### 4.2 前端环境变量 (`/var/www/aiquicksite/frontend/.env.production`):
sudo nano /var/www/aiquicksite/frontend/.env.production



## 5. Nginx 配置


### 5.1 上传SSL证书

####  5.1.1 本地已經创建SSL证书
1、本地准备好证书和私钥
通常你会有两个文件（有时还有中间证书）：
证书文件（如：666ai.net_bundle.crt 或 fullchain.pem）
私钥文件（如：666ai.net.key 或 privkey.pem）
请在本地电脑上准备好这两个文件。

2、在服务器上创建存放目录
sudo mkdir -p /etc/nginx/certs

3、用 scp 上传到服务器

scp ~/Downloads/666ai.net_bundle.crt ubuntu@123.123.123.123:/tmp/

上传私钥文件
scp ~/Downloads/666ai.net.key ubuntu@43.153.65.149:/tmp/

4、移动到 /etc/nginx/certs/
sudo mv /tmp/666ai.net_bundle.crt /etc/nginx/certs/
sudo mv /tmp/666ai.net.key /etc/nginx/certs/

5 设置权限（可选但推荐）
sudo chmod 600 /etc/nginx/certs/666ai.net.key
sudo chmod 644 /etc/nginx/certs/666ai.net_bundle.crt

检查 Nginx 配置路径
测试并重载 Nginx

6检查 Nginx 配置路径是否一致（打开ngix文件检查）
ssl_certificate /etc/nginx/certs/666ai.net_bundle.crt;
ssl_certificate_key /etc/nginx/certs/666ai.net.key;

7. 测试 Nginx 配置并重启
sudo nginx -t
sudo systemctl reload nginx

#### 5.1.2 申請ssl证书

運行命令 

sudo apt install certbot python3-certbot-nginx



sudo mkdir -p /var/www/certbot && sudo chown -R www-data:www-data /var/www/certbot && sudo nginx -t && sudo systemctl reload nginx  # 創建目錄 測試ngnix配置 重新加載ngnix

配置域名服务器，把域名解析到服务器ip 

sudo certbot --nginx #  申请ssl证书 （返回Successfully received certificate.
Certificate is saved at: /etc/letsencrypt/live/aiquicksite.com/fullchain.pem
Key is saved at:         /etc/letsencrypt/live/aiquicksite.com/privkey.pem）

修改niginx配置文件中的ssl 目錄


### 5.2 修改 Nginx 配置文件
```bash
sudo nano /etc/nginx/sites-available/default # 用ctrl+K 快速删除
```
### 5.3 启用 Nginx 配置
```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/aiquicksite /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
cat /etc/nginx/sites-available/aiquicksite
sudo systemctl restart nginx
sudo systemctl reload nginx

# 测试SSL配置
openssl s_client -connect www.aiquicksite.com:443 -servername www.aiquicksite.com
```


## 6. 数据库配置

### 6.1 如果新建数据库的PostgreSQL 配置（如果使用原数据库参考3.3）
```bash
# 切换到 postgres 用户
sudo -i -u postgres 

# 创建数据库
createdb landingpages

# 创建用户并设置密码
createuser -P seuzhy
# 在提示时输入密码 seuzhy161230

# 授予权限
psql -c "GRANT ALL PRIVILEGES ON DATABASE landingpages TO seuzhy;"

# 退出 postgres 用户
exit

# 可能出现授权不足情况,退出后重进入
以postgres用户连接数据库：
sudo -u postgres psql landingpages

执行以下授权命令：
GRANT CREATE ON SCHEMA public TO seuzhy WITH GRANT OPTION;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO seuzhy;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO seuzhy;

验证权限：
SELECT has_schema_privilege('seuzhy', 'public', 'CREATE');


# 退出 postgres 用户
exit
```

### 3.2 Redis 安全配置（7.0+版本）

```bash
# 1. 备份原始配置
sudo cp /etc/redis/redis.conf /etc/redis/redis.conf.bak

# 2. 编辑配置文件
sudo nano /etc/redis/redis.conf

# 修改以下关键安全配置（建议在SECURITY部分）：
# - 设置强密码（取消requirepass注释并修改）
requirepass redis123

# - 只允许本地访问（取消bind注释）
bind 127.0.0.1 ::1

# - 启用保护模式
protected-mode yes

# - 禁用危险命令（推荐）
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG ""
rename-command SHUTDOWN ""
rename-command DEBUG ""
rename-command KEYS ""

# - 限制内存使用（根据服务器配置调整）
maxmemory 2gb
maxmemory-policy allkeys-lru

# 3. 重启服务
sudo systemctl restart redis-server

# 4. 验证配置生效
# 方式一：密码验证
redis-cli
AUTH redis123
PING
# 应返回 "PONG"

# 方式二：绑定限制验证
redis-cli -h 127.0.0.1 -a your_strong_password_here ping
# 应返回 "PONG"

# 方式三：外部访问测试（应失败）
redis-cli -h 服务器公网IP ping
# 应返回连接拒绝

# 6. 检查绑定限制
sudo ss -tulnp | grep redis
# 应只显示 127.0.0.1:6379 和 [::1]:6379

# 7. 检查保护模式
redis-cli -a your_strong_password_here config get protected-mode
# 应返回 "protected-mode" "yes"
```

> **安全建议：**
> 1. 使用至少32位复杂密码替换your_strong_password_here
> 2. 定期检查Redis日志：`sudo tail -f /var/log/redis/redis-server.log`
> 3. 配置防火墙限制访问：
>    ```bash
>    sudo ufw allow from 127.0.0.1 to any port 6379
>    sudo ufw deny 6379
>    ```
> 4. 重要数据应定期备份：
>    ```bash
>    redis-cli -a your_password save
>    sudo cp /var/lib/redis/dump.rdb /backup/redis_dump_$(date +%Y%m%d).rdb
>    ```
> 5. Redis升级步骤：
>    ```bash
>    # 检查当前版本
>    redis-server --version
>
>    # 停止Redis服务
>    sudo systemctl stop redis-server
>
>    # 备份配置和数据
>    sudo cp /etc/redis/redis.conf /etc/redis/redis.conf.bak
>    sudo cp /var/lib/redis/dump.rdb /var/lib/redis/dump.rdb.bak
>
>    # 升级Redis
>    sudo apt update
>    sudo apt install --only-upgrade redis-server
>
>    # 启动服务
>    sudo systemctl start redis-server
>
>    # 验证升级
>    redis-server --version
>    redis-cli -a your_password ping
>    ```
> 6. 监控Redis性能：`redis-cli -a your_password --stat`
> 7. 检查已知漏洞：定期查看 https://redis.io/topics/security

### 3.3 数据库備份還原

### 完整備份還原流程

1. **备份阶段（原服务器）**：
```bash
# 全库备份
pg_dumpall -U postgres -f /backup/full_backup_$(date +%Y%m%d).sql

# 单库备份（推荐）
pg_dump -U ai_learning_user -Fc -f /backup/enterprise_ai_classroom_$(date +%Y%m%d).dump enterprise_ai_classroom

# 本地备份
pg_dump -U seuzhy -h localhost -p 5432 landingpages > backup_$(date +%Y%m%d).sql

scp backup_$(date +%Y%m%d).sql ubuntu@**************:/backup/$(date +%Y%m%d).sql
```


2. **還原准备（新服务器）**：
```bash
# 创建相应用户和数据库
sudo -u postgres psql -c "CREATE USER ai_learning_user WITH PASSWORD 'your_password';"
sudo -u postgres psql -c "CREATE DATABASE enterprise_ai_classroom OWNER ai_learning_user;"

# 修改pg_hba.conf认证方式
sudo nano /etc/postgresql/16/main/pg_hba.conf
# 添加：host all all 127.0.0.1/32 md5
sudo systemctl restart postgresql
```

3. **恢复阶段（新服务器）**：
```bash
# 全库恢复
psql -U postgres -f /backup/full_backup_YYYYMMDD.sql

# 单库恢复（推荐）
pg_restore -U ai_learning_user -d enterprise_ai_classroom -Fc /backup/enterprise_ai_classroom_YYYYMMDD.dump
```

4. **验证阶段**：
```bash
psql -U ai_learning_user -d enterprise_ai_classroom -c "\dt"
psql -U ai_learning_user -d enterprise_ai_classroom -c "SELECT count(*) FROM your_main_table;"
```

> **关键说明**：
> - 备份导入可以完全替代迁移（同版本PostgreSQL）
> - 生产环境建议使用pg_dump/pg_restore的-Fc格式
> - 大型数据库建议在低峰期操作

## 8 启动服务
### 8.1 构建前端
cd frontend
rm -rf node_modules && rm -f package-lock.json && rm -f pnpm-lock.yaml
pnpm store prune
NODE_ENV=production pnpm install
NODE_ENV=production pnpm build


### 8.2 构建后端
cd backend
rm -rf node_modules
rm -rf dist 
rm -f package-lock.json
rm -f pnpm-lock.yaml
pnpm store prune
NODE_ENV=production pnpm install
NODE_ENV=production pnpm build

执行数据库迁移（可选）（需要先重新构建后端，迁移后再构建一次）
NODE_ENV=production pnpm run typeorm migration:run -d dist/config/typeorm.config.js

如果没有修改实体文件，则不需要重新构建实体
NODE_ENV=production pnpm install
NODE_ENV=production pnpm build


### 8.3 启动前后端服务
```bash
pm2 start pnpm --name "aiquicksite-frontend" -- start
cd /var/www/aiquicksite/backend
NODE_ENV=production pm2 start dist/main.js --name "aiquicksite-backend" --env production

# 设置开机自启
pm2 save
pm2 startup
```

## 9. 维护命令

### 9.1 查看服务状态
```bash
# 查看 PM2 进程
pm2 status

# 查看后端日志
pm2 logs aiquicksite-backend

# 查看 Nginx 访问日志
sudo tail -f /var/log/nginx/access.log

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

### 9.2 重启服务
```bash
# 重启后端
pm2 restart aiquicksite-backend
pm2 restart aiquicksite-frontend

# 重启 Nginx
sudo systemctl restart nginx

# 重启 Redis
sudo systemctl restart redis

# 重启 PostgreSQL
sudo systemctl restart postgresql
```

### 9.3 数据库备份
```bash
# PostgreSQL 备份
pg_dump -U ai_learning_user enterprise_ai_classroom > backup_$(date +%Y%m%d).sql
```

## 10. 安全建议

1. 配置防火墙（UFW）
```bash
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

2. 设置 SSL 证书（推荐使用 Let's Encrypt）
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

3. 定期更新系统
```bash
sudo apt update
sudo apt upgrade
```

## 11. 故障排查

### 11.1 检查服务状态
```bash
# 检查 PostgreSQL 状态
sudo systemctl status postgresql

# 检查 Redis 状态
sudo systemctl status redis

# 检查 Nginx 状态
sudo systemctl status nginx

# 检查后端服务状态
pm2 status
```

### 11.2 常见问题解决

1. 如果 Nginx 无法启动：
   - 检查配置文件语法：`sudo nginx -t`
   - 检查日志：`sudo tail -f /var/log/nginx/error.log`

2. 如果数据库连接失败：
   - 检查 PostgreSQL 是否运行：`sudo systemctl status postgresql`
   - 检查数据库连接配置
   - 验证数据库用户权限

3. 如果 Redis 连接失败：
   - 检查 Redis 是否运行：`sudo systemctl status redis`
   - 验证 Redis 密码配置
   - 检查 Redis 日志：`sudo tail -f /var/log/redis/redis-server.log`

4. ngnix或者環境文件都必須要正確配置 特別是域名、ip地址等 

5、 environment.ts cookie domain被硬编码为".666ai.net"

## 10. 注意事项

1. 所有密码都应使用强密码，不要使用示例中的密码
2. 定期备份数据库
3. 监控服务器资源使用情况
4. 保持系统和依赖包更新
5. 定期检查日志文件
6. 配置文件权限设置正确
7. 重要配置更改前先备份

## 12. 性能优化建议

1. 配置 Nginx 缓存
2. 优化 PostgreSQL 配置
3. 配置 Redis 持久化
4. 使用 PM2 集群模式（如需要）
5. 配置日志轮转
6. 监控系统资源使用 


# 2、更新代码再次部署

### 1、登录服务器
ssh ubuntu@**************

### 2、进入项目目录
cd /var/www/aiquicksite

export NODE_ENV=production

### 3、拉取最新代码
#### git 命令
git status
git add .#
git checkout . && git clean -fd #放弃本地更改
git commit -a
git pull

### 4、构建前端

cd frontend
export NODE_ENV=production
sudo rm -rf node_modules && rm -f package-lock.json && rm -f pnpm-lock.yaml 
sudo rm -rf .next 
pnpm store prune
NODE_ENV=production pnpm install
NODE_ENV=production pnpm build

### 5、构建后端
cd backend
export NODE_ENV=production
rm -rf node_modules
rm -rf dist 
rm -f package-lock.json
rm -f pnpm-lock.yaml
pnpm store prune
NODE_ENV=production pnpm install
NODE_ENV=production pnpm build

执行数据库迁移（可选）（需要先重新构建后端，迁移后再构建一次）
NODE_ENV=production pnpm run typeorm migration:run -d dist/config/typeorm.config.js



### 6、重启 Nginx（可选）
cat /etc/nginx/sites-available/aiquicksite
sudo systemctl restart nginx

### 7、重启前后端服务
cd ..
pm2 restart aiquicksite-backend
pm2 restart aiquicksite-frontend

pm2 start aiquicksite-backend

pm2 start dist/main.js --name "aiquicksite-backend" --env production

如果要关了以后重启
cd /var/www/aiquicksite/backend
pm2 delete aiquicksite-backend
pm2 delete aiquicksite-frontend
NODE_ENV=production pm2 start dist/main.js --name "aiquicksite-backend"

如果要关了以后重启
cd /var/www/aiquicksite/frontend
pm2 delete aiquicksite-frontend
NODE_ENV=production pm2 start npm --name "aiquicksite-frontend" -- start

pm2 save

### 8、 查看服务状态
pm2 status
pm2 logs aiquicksite-backend
pm2 logs aiquicksite-backend --lines 100
pm2 logs aiquicksite-backend --lines 1000 | cat


# 3、数据库命令集合

sudo -i -u postgres
##切换数据库
psql -U seuzhy landingpages

### 查看数据库列表
\l
### 切换到数据库
\c landingpages
### 查看表列表
\dt
### 查看表结构
\d table_name
### 查看字段
\df table_name
### 搜索字段
\df* field_name

### 查看管理员记录
SELECT * FROM users WHERE role = 'admin';

### 查看用户当前状态
SELECT * FROM users WHERE email = '<EMAIL>';

### 删除管理员账号
DELETE FROM users WHERE role = 'admin';

### 把普通用户变成管理员账号
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';

### 退出 PostgreSQL
\q

### 退出 postgres 用户
exit

# 4、 本地数据库命令
psql postgres (开启数据库命令)
pnpm run migration:run （运行迁移）
pnpm run migration:revert （回转迁移）